# Easy Coder 自托管部署指南

## 1. 服务器要求
- Node.js >=18.18.0
- npm >=9.0.0
- 数据库: SQLite (默认) 或 PostgreSQL/MySQL
- 内存: 至少2GB
- 存储: 至少1GB可用空间

## 2. 安装步骤

### 2.1 克隆代码库
```bash
git clone https://github.com/your-repo/easy-coder.git
cd easy-coder
```

### 2.2 安装依赖
```bash
npm install
```

### 2.3 配置环境变量
复制`.example.env`为`.env`并修改:
```bash
cp .example.env .env
```

## 3. 生产环境构建
```bash
npm run build
```

## 4. 数据库设置
```bash
npx prisma migrate deploy
npx prisma generate
```

## 5. 启动服务

### 5.1 直接启动 (开发模式)
```bash
npm run dev
```

### 5.2 生产模式 (推荐使用PM2)
```bash
npm install -g pm2
pm2 start npm --name "easy-coder" -- start
pm2 save
pm2 startup
```

## 6. 配置反向代理 (可选)
建议使用Nginx作为反向代理:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 7. 维护命令
- 查看日志: `pm2 logs easy-coder`
- 重启服务: `pm2 restart easy-coder`
- 停止服务: `pm2 stop easy-coder`
- 更新代码后重新部署:
  ```bash
  git pull
  npm install
  npm run build
  npx prisma migrate deploy
  pm2 restart easy-coder
  ```

## 8. 故障排查
- 检查日志: `pm2 logs`
- 验证数据库连接
- 检查端口3000是否被占用
- 确保.env配置正确