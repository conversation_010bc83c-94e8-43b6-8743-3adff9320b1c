# 刷新页面后自定义模型不显示问题修复

## 问题描述
保存模型，刷新页面后，新加的自定义模型没有显示

## 问题根本原因

### 1. 初始化时序问题
```typescript
// AI设置组件初始化时
const [localModels, setLocalModels] = useState<CustomModel[]>(customModels);
```

在页面刷新时：
1. 全局store的`customModels`初始值为空数组 `[]`
2. AI设置组件的`localModels`也被初始化为空数组 `[]`
3. 即使后来`initializeFromCookies`加载了正确数据到全局store
4. AI设置组件的`localModels`仍然是空的，因为它只在首次初始化时从Cookie加载

### 2. 状态同步缺失
- 全局store通过`initializeFromCookies`正确加载了自定义模型
- 但AI设置组件的本地状态没有同步全局store的更新
- 导致AI设置界面显示空的模型列表

## 修复方案

### 1. 修改本地状态初始化
```typescript
// 修改前：从全局store初始化（可能为空）
const [localModels, setLocalModels] = useState<CustomModel[]>(customModels);

// 修改后：始终从空数组开始
const [localModels, setLocalModels] = useState<CustomModel[]>([]);
```

### 2. 添加状态同步机制
```typescript
// 同步全局store的customModels到本地状态
useEffect(() => {
  if (customModels.length > 0 && localModels.length === 0 && !isInitialized) {
    console.log('🔍 同步全局store的customModels到本地状态:', customModels);
    setLocalModels(customModels);
    setIsInitialized(true);
  }
}, [customModels, localModels.length, isInitialized]);
```

### 3. 添加调试信息
```typescript
// 在initializeFromCookies中添加调试
console.log('🔍 从Cookie加载的配置:', {
  provider: config.provider,
  model: config.model,
  customModelsCount: config.customModels.length,
  customModels: config.customModels
});

// 在AI设置组件中添加调试
console.log('🔍 AI设置组件获取的配置:', {
  customModelsCount: config.customModels.length,
  customModels: config.customModels
});
```

## 修复后的执行流程

### 页面刷新时的完整流程：

1. **页面加载**
   - 全局store初始化：`customModels: []`, `isInitialized: false`
   - AI设置组件初始化：`localModels: []`, `isInitialized: false`

2. **initializeFromCookies执行**
   - 从Cookie读取自定义模型数据
   - 更新全局store：`customModels: [...]`, `isInitialized: true`

3. **状态同步触发**
   - 检测到`customModels.length > 0`且`localModels.length === 0`
   - 同步全局数据到本地：`setLocalModels(customModels)`
   - 标记为已初始化：`setIsInitialized(true)`

4. **AI设置界面显示**
   - 显示正确的自定义模型列表
   - 用户可以看到之前保存的模型

## 测试步骤

### 测试1: 基本保存和刷新
1. 添加自定义模型（如：gemini-2.0-flash）
2. 保存配置
3. 刷新页面
4. 打开AI设置
5. **预期结果**: 自定义模型正确显示

### 测试2: 多个模型
1. 添加多个自定义模型
2. 保存配置
3. 刷新页面
4. 打开AI设置
5. **预期结果**: 所有自定义模型都正确显示

### 测试3: 不同提供商
1. 在OpenAI下添加模型A
2. 切换到DeepSeek，添加模型B
3. 保存配置
4. 刷新页面
5. 打开AI设置，切换提供商
6. **预期结果**: 每个提供商下的模型都正确显示

### 测试4: 调试信息验证
1. 打开浏览器开发者工具
2. 刷新页面
3. 查看控制台输出
4. **预期结果**: 看到调试信息显示正确的模型加载过程

## 调试信息示例

正常情况下，控制台应该显示：

```
🔍 initializeFromCookies 被调用，当前初始化状态: false
🔍 开始从Cookie加载配置...
🔍 从Cookie读取自定义模型: [{"id":"gemini-2.0-flash","name":"Gemini 2.0 Flash","provider":"openai","maxTokens":8192,"temperature":0.7}]
🔍 解析后的自定义模型: [{id: "gemini-2.0-flash", name: "Gemini 2.0 Flash", ...}]
🔍 从Cookie加载的配置: {provider: "openai", model: "gpt-4o", customModelsCount: 1, customModels: [...]}
🔍 全局状态已更新，自定义模型数量: 1
🔍 同步全局store的customModels到本地状态: [{id: "gemini-2.0-flash", ...}]
```

## 技术细节

### Cookie存储格式
```json
{
  "ai_custom_models": "[{\"id\":\"gemini-2.0-flash\",\"name\":\"Gemini 2.0 Flash\",\"provider\":\"openai\",\"maxTokens\":8192,\"temperature\":0.7}]"
}
```

### 状态管理架构
```
页面刷新
    ↓
全局Store初始化 (customModels: [])
    ↓
initializeFromCookies() 
    ↓
从Cookie加载数据
    ↓
更新全局Store (customModels: [...])
    ↓
触发useEffect同步
    ↓
更新AI设置组件本地状态
    ↓
显示正确的模型列表
```

### 关键修复点
1. **避免错误的初始化依赖**: 不再依赖可能为空的全局store初始值
2. **添加状态同步机制**: 监听全局store变化并同步到本地状态
3. **保持初始化状态一致性**: 确保组件和全局的初始化状态协调
4. **添加调试信息**: 便于排查问题和验证修复效果

## 预期修复效果

修复后，用户将享受到：
- ✅ 刷新页面后自定义模型正确显示
- ✅ 多个模型和多个提供商都正常工作
- ✅ 配置加载过程透明可调试
- ✅ 状态管理逻辑清晰可靠

这个修复确保了自定义模型的完全可靠性，解决了"保存模型，刷新页面，新加的自定义模型没有显示"的问题。
