# 内容生成器用户指南

## 简介

内容生成器是一个强大的工具，允许您通过对话方式生成HTML和Markdown内容，并实时预览渲染效果。无论您是想快速创建网页原型，还是生成格式化文档，内容生成器都能帮助您高效完成任务。

## 快速开始

### 访问内容生成器

1. 打开浏览器，访问 http://localhost:4000/content-generator
2. 您将看到一个分为左右两部分的界面：
   - 左侧：对话面板，用于与AI进行交互
   - 右侧：内容预览面板，用于显示生成的内容

### 生成您的第一个内容

1. 在左侧对话输入框中，输入您的需求，例如：
   - "写个简单的hello world网页"
   - "生成一个产品展示页面"
   - "创建一个简单的Markdown文档"

2. 按下回车键或点击发送按钮
3. AI将生成回复，包含相应的HTML或Markdown代码
4. 系统会自动从回复中提取代码，并在右侧面板中显示渲染效果

### 切换视图模式

右侧面板提供三种视图模式：

- **代码**：显示原始代码
- **预览**：显示渲染效果
- **分屏**：同时显示代码和预览

点击右上角的视图模式按钮即可切换。

## 高级功能

### 选择内容类型

您可以在左侧面板顶部选择要生成的内容类型：

- **HTML**：生成网页内容
- **Markdown**：生成格式化文档

### 选择AI模型

您可以选择不同的AI模型来生成内容：

- **GPT-3.5 Turbo**：快速响应，适合简单任务
- **GPT-4**：更高质量，适合复杂任务

### 自定义生成选项

您可以通过以下选项自定义生成的内容：

- **样式**：选择内容的视觉风格（简约现代、商务专业等）
- **复杂度**：选择内容的复杂程度（简单、中等、复杂）

## 使用技巧

### 提示工程

为获得最佳结果，请尝试以下提示技巧：

1. **明确需求**：清晰描述您想要的内容类型和功能
   - 好的例子："创建一个包含导航栏、英雄区和三列特性展示的登录页面"
   - 不好的例子："做个网页"

2. **指定细节**：提供关于样式、颜色、布局的具体细节
   - 好的例子："使用蓝色主题，包含圆角按钮和卡片式布局"
   - 不好的例子："做得好看点"

3. **迭代改进**：基于初始结果提出修改建议
   - 好的例子："这看起来不错，但能否将导航栏固定在顶部，并添加一个联系表单？"
   - 不好的例子："改一下"

### 常见命令

以下是一些有用的命令示例：

- "生成一个响应式导航栏"
- "创建一个联系表单，包含姓名、邮箱和消息字段"
- "设计一个产品卡片组件，包含图片、标题、描述和价格"
- "编写一个Markdown格式的项目README文件"
- "创建一个简单的博客文章模板"

## 故障排除

### 内容不显示

如果右侧面板没有显示内容：

1. 确保您的提示中明确要求生成HTML或Markdown内容
2. 检查AI回复中是否包含代码块（通常用```包围）
3. 尝试刷新页面后重试

### 预览不正确

如果预览效果与预期不符：

1. 切换到代码视图，检查生成的代码是否完整
2. 确保代码没有语法错误
3. 尝试修改您的提示，提供更具体的要求

### 性能问题

如果遇到性能问题：

1. 尝试生成较小的内容片段
2. 减少页面上的其他活动标签页
3. 确保您的网络连接稳定

## 示例

### HTML示例

输入：
```
写一个简单的登录表单
```

生成结果：
```html
<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录表单</title>
  <style>
    body {
      font-family: 'Arial', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f5f5f5;
      margin: 0;
    }
    .login-form {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 350px;
    }
    h2 {
      text-align: center;
      margin-bottom: 1.5rem;
      color: #333;
    }
    .form-group {
      margin-bottom: 1rem;
    }
    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #555;
    }
    input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 1rem;
    }
    button {
      width: 100%;
      padding: 0.75rem;
      background-color: #4a6cf7;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 1rem;
      cursor: pointer;
      margin-top: 1rem;
    }
    button:hover {
      background-color: #3a5cf7;
    }
    .forgot-password {
      text-align: center;
      margin-top: 1rem;
    }
    .forgot-password a {
      color: #4a6cf7;
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="login-form">
    <h2>用户登录</h2>
    <form>
      <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" name="username" placeholder="请输入用户名" required>
      </div>
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" name="password" placeholder="请输入密码" required>
      </div>
      <button type="submit">登录</button>
      <div class="forgot-password">
        <a href="#">忘记密码？</a>
      </div>
    </form>
  </div>
</body>
</html>
```

### Markdown示例

输入：
```
写一个项目README文件
```

生成结果：
```markdown
# 项目名称

一个简洁而强大的项目描述，简明扼要地说明项目的主要功能和目的。

## 功能特性

- **特性一**：描述项目的第一个主要特性
- **特性二**：描述项目的第二个主要特性
- **特性三**：描述项目的第三个主要特性
- **特性四**：描述项目的第四个主要特性

## 安装指南

### 前置要求

- Node.js v14.0.0 或更高版本
- npm v6.0.0 或更高版本
- 其他依赖项...

### 安装步骤

1. 克隆仓库
   ```bash
   git clone https://github.com/username/project-name.git
   cd project-name
   ```

2. 安装依赖
   ```bash
   npm install
   ```

3. 配置环境变量
   ```bash
   cp .env.example .env
   # 编辑.env文件，填入必要的配置信息
   ```

4. 启动项目
   ```bash
   npm start
   ```

## 使用示例

提供一些基本的使用示例，帮助用户快速上手：

```javascript
// 示例代码
const project = require('project-name');
const result = project.doSomething();
console.log(result);
```

## API文档

### `function1(param)`

描述这个函数的功能和用途。

**参数：**
- `param` (String): 参数的描述

**返回值：**
- (Number): 返回值的描述

**示例：**
```javascript
const result = function1('test');
// 返回: 42
```

### `function2(param1, param2)`

描述这个函数的功能和用途。

**参数：**
- `param1` (Object): 第一个参数的描述
- `param2` (Array): 第二个参数的描述

**返回值：**
- (Boolean): 返回值的描述

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

该项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

项目维护者 - [@username](https://twitter.com/username) - <EMAIL>

项目链接: [https://github.com/username/project-name](https://github.com/username/project-name)
```

## 结语

内容生成器是一个强大的工具，可以帮助您快速创建HTML和Markdown内容。通过对话方式与AI交互，您可以轻松生成各种类型的内容，并实时预览渲染效果。

随着您对工具的熟悉，您将能够更有效地利用它来加速您的开发和内容创建工作流程。如有任何问题或建议，请随时联系我们的支持团队。

祝您使用愉快！
