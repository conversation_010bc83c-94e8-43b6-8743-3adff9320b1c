---
description: 系统地分析和开发新需求，确保与现有项目无缝衔接
---

<context>
1、分析需求的完成需要哪些核心技术、框架、类型、机制、组件、模块等可复用的代码，尽量找到项目已有成功实现的案例进行参考
2、扫描项目中与需求相关的代码，提供充分的技术背景信息
3、在项目已有信息基础上进行新的开发设计，确保链条是通的，无技术风险
4、拆分全链路的开发任务，依次完成
</context>

<instructions>
1. 进行需求技术分析：
   - 识别所需的核心技术组件
   - 列出涉及的框架和机制
   - 梳理可复用的代码模块
   - 寻找项目中类似的实现案例

2. 执行项目代码扫描：
   - 搜索相关业务代码
   - 分析现有技术实现
   - 整理技术文档
   - 收集接口信息

3. 制定开发设计方案：
   - 确认技术可行性
   - 设计系统架构
   - 评估技术风险
   - 制定解决方案，说明需要修改哪些文件，需要修改哪些代码

4. 任务分解与排期：
   - 按模块拆分任务
   - 确定开发顺序

5. 进行开发实施：
   - 分步骤依次进行开发
   - 进行单元测试（如需）
   - 确保模块间集成
   - 执行系统测试
</instructions>