/**
 * xAI Provider实现
 * 支持Grok模型
 * 注意：xAI的API与OpenAI兼容，所以我们可以复用OpenAI的客户端
 */

import { OpenAI } from 'openai';
import { BaseProvider, ChatCompletionOptions, ChatCompletionResponse, Message, ProviderConfig } from './base';
import { getModelById } from '../models';

export class XAIProvider extends BaseProvider {
  private client: OpenAI | null = null;

  constructor(config: ProviderConfig) {
    super('xai', config);
    this.initClient();
  }

  /**
   * 初始化xAI客户端
   */
  private initClient(): void {
    try {
      if (!this.validateConfig()) {
        console.warn('xAI配置不完整，将使用默认配置');
        // 使用默认配置而不是抛出错误
        this.client = new OpenAI({
          apiKey: this.config.apiKey || 'sk-placeholder',
          baseURL: this.config.baseUrl || 'https://api.x.ai/v1',
          dangerouslyAllowBrowser: true, // 允许在浏览器环境中使用
        } as const);
        return;
      }

      this.client = new OpenAI({
        apiKey: this.config.apiKey,
        baseURL: this.config.baseUrl || 'https://api.x.ai/v1',
        dangerouslyAllowBrowser: true, // 允许在浏览器环境中使用
      } as const);
    } catch (error) {
      console.error('初始化xAI客户端失败:', error);
      this.client = null;
    }
  }

  /**
   * 验证配置
   */
  validateConfig(): boolean {
    // 允许没有API密钥，但会在日志中显示警告
    return true;
  }

  /**
   * 转换消息格式
   */
  private convertMessages(messages: Message[]): any[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
  }

  /**
   * 聊天完成方法
   */
  async chatCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    if (!this.client) {
      this.initClient();
      if (!this.client) {
        throw new Error('xAI客户端未初始化');
      }
    }

    try {
      const modelConfig = getModelById(options.model);
      if (!modelConfig) {
        throw new Error(`未找到模型配置: ${options.model}`);
      }

      // 检查是否有有效的API密钥
      if (!this.config.apiKey || this.config.apiKey === 'sk-placeholder') {
        return {
          content: '请在AI设置中配置xAI API密钥后再使用此模型。',
          model: options.model,
          usage: {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0
          }
        };
      }

      const response = await this.client.chat.completions.create({
        model: options.model,
        messages: this.convertMessages(options.messages),
        temperature: options.temperature ?? modelConfig.temperature ?? 0.7,
        max_tokens: options.maxTokens ?? modelConfig.maxTokens ?? 4096,
        stream: false
      });

      return {
        content: response.choices[0]?.message?.content || '',
        model: options.model,
        usage: {
          promptTokens: response.usage?.prompt_tokens || 0,
          completionTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0
        }
      };
    } catch (error) {
      console.error('xAI聊天完成失败:', error);
      throw error;
    }
  }

  /**
   * 流式聊天完成方法
   */
  async streamingChatCompletion(
    options: ChatCompletionOptions,
    onContent: (content: string) => void,
    onError: (error: Error) => void,
    onFinish: (response: ChatCompletionResponse) => void
  ): Promise<void> {
    if (!this.client) {
      this.initClient();
      if (!this.client) {
        onError(new Error('xAI客户端未初始化'));
        return;
      }
    }

    try {
      const modelConfig = getModelById(options.model);
      if (!modelConfig) {
        onError(new Error(`未找到模型配置: ${options.model}`));
        return;
      }

      // 检查是否有有效的API密钥
      if (!this.config.apiKey || this.config.apiKey === 'sk-placeholder') {
        onContent('请在AI设置中配置xAI API密钥后再使用此模型。');
        onFinish({
          content: '请在AI设置中配置xAI API密钥后再使用此模型。',
          model: options.model,
          usage: {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0
          }
        });
        return;
      }

      const stream = await this.client.chat.completions.create({
        model: options.model,
        messages: this.convertMessages(options.messages),
        temperature: options.temperature ?? modelConfig.temperature ?? 0.7,
        max_tokens: options.maxTokens ?? modelConfig.maxTokens ?? 4096,
        stream: true
      });

      let fullContent = '';
      let usage = {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      };

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          fullContent += content;
          onContent(content);
        }
      }

      // 流式响应完成
      onFinish({
        content: fullContent,
        model: options.model,
        usage
      });
    } catch (error) {
      console.error('xAI流式聊天完成失败:', error);
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  }
}
