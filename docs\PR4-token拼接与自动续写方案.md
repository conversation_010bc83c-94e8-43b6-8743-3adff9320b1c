# PR#4 Token截断与自动续写拼接——详细设计与全局影响分析

## 一、设计目标

- 实现流式输出的Token截断与自动续写拼接，确保内容拼接无重叠/断裂，异常可自动/手动恢复。
- 兼容多Provider（OpenAI/Anthropic/DeepSeek等），为未来支持offset/hash协议预留接口。
- 统一流式事件协议，增强拼接健壮性与异常恢复能力。

## 二、全局影响与变更点

### 1. 协议与类型层

- **扩展流式事件类型**：为`FileChunkEvent`等增加`contentHash/hash`字段，定义拼接校验协议（即使部分Provider暂不支持）。
- **事件解析器**：在`lib/streaming/stream-parser.ts`中解析并透传hash字段。

### 2. 拼接与状态管理层

- **拼接上下文管理**：在`lib/streaming/stream-handler.ts`中，按fileId维护拼接上下文，记录offset、hash、chunk序列。
- **拼接校验与异常处理**：
  - 校验offset连续性（如offset断裂/重叠则触发异常）。
  - 校验hash（如有，优先用hash校验内容拼接正确性）。
  - 拼接失败时自动重试（如支持offset/hash则发起续写，否则降级为整体刷新或手动恢复）。
- **降级与回退机制**：Provider不支持hash/offset时，拼接失败可降级为整体刷新或提示用户手动恢复。

### 3. Provider适配层

- **OpenAI/Anthropic**：当前不支持offset/hash，拼接层需降级处理，未来如API升级可无缝接入。
- **DeepSeek等**：如支持offset/hash，直接走协议拼接与自动续写。

### 4. 监控与日志

- 关键拼接流程增加详细日志，便于定位拼接失败、内容断裂、重叠等问题。
- 监控拼接异常、自动重试、降级等事件，便于后续优化。

### 5. UI与交互

- 拼接异常时，UI需提示用户可手动重试或回退。
- 自动续写/拼接恢复时，UI可展示进度与状态。

---

## 三、核心数据流与状态流（Mermaid时序图）

```mermaid
sequenceDiagram
    participant Provider as AI Provider
    participant Parser as StreamParser
    participant Handler as StreamHandler
    participant UI as ContentViewer/UI

    Provider->>Parser: SSE流事件（含offset/hash）
    Parser->>Handler: 结构化事件对象
    Handler->>Handler: 拼接offset/hash校验
    alt 拼接成功
        Handler->>UI: 内容追加/渲染
    else 拼接失败
        Handler->>Handler: 自动重试/降级
        Handler->>UI: 异常提示/回退
    end
    Handler->>Provider: 需要时自动发起offset续写
```

---

## 四、主要变更文件清单

- `lib/streaming/stream-event-types.ts`：扩展事件类型，增加hash字段。
- `lib/streaming/stream-parser.ts`：解析hash字段，透传到事件对象。
- `lib/streaming/stream-handler.ts`：拼接上下文管理、offset/hash校验、异常重试与降级。
- `lib/providers/openai.ts`、`lib/providers/anthropic.ts`：Provider协议适配与降级处理。
- `components/content-viewer/content-viewer.tsx`：拼接异常时的UI提示与手动恢复入口。

---

## 五、异常与回退策略

- **拼接失败**：如offset/hash不连续，自动重试（如Provider支持），否则降级为整体刷新或提示用户手动恢复。
- **Provider不支持hash/offset**：拼接层降级为顺序拼接，异常时提示用户。
- **全局回退**：保留非流式分支，任何环节异常可一键切回非流式模式。

---

## 六、验收标准

- Token截断与自动续写拼接无重叠/断裂，异常可自动/手动恢复。
- 兼容多Provider，协议层可平滑扩展。
- 拼接异常有详细日志与UI提示，用户可感知并恢复。
- 回退机制健全，切换无副作用。

---