# 构建+部署打包脚本（适用于方案二/常规部署，Windows PowerShell 版）

# 1. 安装依赖
npm install

# 2. 构建生产包
npm run build

# 3. 复制部署所需文件到 deploy-dist 目录
Remove-Item -Recurse -Force deploy-dist -ErrorAction SilentlyContinue
New-Item -ItemType Directory -Path deploy-dist
Copy-Item .next -Destination deploy-dist -Recurse
Copy-Item package.json -Destination deploy-dist
Copy-Item node_modules -Destination deploy-dist -Recurse
Copy-Item public -Destination deploy-dist -Recurse
Copy-Item next.config.js -Destination deploy-dist -ErrorAction SilentlyContinue
Copy-Item package-lock.json -Destination deploy-dist -ErrorAction SilentlyContinue
Copy-Item prisma -Destination deploy-dist -Recurse -ErrorAction SilentlyContinue
Copy-Item .env* -Destination deploy-dist -ErrorAction SilentlyContinue

Write-Host "✅ 构建完成，部署文件已生成在 deploy-dist 目录"