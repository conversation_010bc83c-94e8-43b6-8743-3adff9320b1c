'use client';

import React, { useRef, useEffect, useState } from 'react';
import { HtmlPreviewProps } from './types';
import EditableHtmlPreview from './editable-html-preview';

const HtmlPreview: React.FC<HtmlPreviewProps> = ({ content, onChange }) => {
  const [isEditing, setIsEditing] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (!content || !iframeRef.current) {
      console.log('No content or iframe ref');
      return;
    }

    const iframe = iframeRef.current;
    const doc = iframe.contentDocument || iframe.contentWindow?.document;

    if (!doc) {
      console.log('No document in iframe');
      return;
    }

    try {
      // 记录内容长度和前100个字符
      console.log(`Rendering HTML content, length: ${content.length}`);
      console.log(`Content preview: ${content.substring(0, 100)}...`);
      console.log('Full content:', content);

      // 确保内容是完整的HTML文档
      let htmlContent = content;
      if (!content.includes('<!DOCTYPE html>') && !content.includes('<html')) {
        console.log('Content is not a complete HTML document, wrapping it');
        htmlContent = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HTML Preview</title>
  <style>
    body { font-family: system-ui, sans-serif; line-height: 1.5; padding: 1rem; }
    pre { background: #f5f5f5; padding: 1rem; border-radius: 0.25rem; overflow: auto; }
    code { font-family: monospace; }
  </style>
</head>
<body>
  ${content}
</body>
</html>`;
      }

      // 写入内容到iframe
      doc.open();
      doc.write(htmlContent);
      doc.close();

      // 设置iframe样式
      iframe.style.height = '100%';
      iframe.style.width = '100%';

      // 处理iframe内部链接
      const links = doc.querySelectorAll('a');
      links.forEach(link => {
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
      });

      // 添加额外样式
      const style = doc.createElement('style');
      style.textContent = `
        body { font-family: system-ui, sans-serif; line-height: 1.5; }
        pre { background: #f5f5f5; padding: 1rem; border-radius: 0.25rem; overflow: auto; }
        code { font-family: monospace; }
      `;
      doc.head.appendChild(style);
    } catch (error) {
      console.error('Error rendering HTML:', error);

      // 显示错误信息和原始内容
      doc.open();
      doc.write(`<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HTML Preview Error</title>
  <style>
    body { font-family: system-ui, sans-serif; line-height: 1.5; padding: 1rem; }
    pre { background: #f5f5f5; padding: 1rem; border-radius: 0.25rem; overflow: auto; white-space: pre-wrap; }
    .error { color: red; }
  </style>
</head>
<body>
  <div class="error">Error rendering HTML content</div>
  <pre>${content.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
</body>
</html>`);
      doc.close();
    }
  }, [content]);

  // 检测是否为slide内容
  const isSlideContent = content && (content.includes('<div class="slide"') || content.includes('class="slide'));
  
  // 为slide内容添加特殊处理
  useEffect(() => {
    if (isSlideContent && iframeRef.current) {
      // 确保容器有足够的高度
      const parentElement = iframeRef.current.parentElement;
      if (parentElement) {
        parentElement.style.minHeight = '600px';
      }
    }
  }, [isSlideContent]);
  
  // 处理编辑保存
  const handleSave = (newContent: string) => {
    setIsEditing(false);
    if (onChange) {
      onChange(newContent);
    }
  };

  // 处理取消编辑
  const handleClose = () => {
    setIsEditing(false);
  };

  // 编辑按钮
  const EditButton = () => (
    <button
      onClick={() => setIsEditing(true)}
      className="absolute top-2 right-2 z-10 px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 transition-colors flex items-center gap-1"
      title="编辑HTML"
    >
      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
      </svg>
      编辑
    </button>
  );

  // 如果处于编辑模式，显示编辑器
  if (isEditing) {
    return (
      <div className="w-full h-full">
        <EditableHtmlPreview 
          content={content} 
          onSave={handleSave} 
          onClose={handleClose} 
        />
      </div>
    );
  }

  return (
    <div className="relative bg-white w-full h-full" style={{ minHeight: isSlideContent ? '800px' : '600px' }}>
      {/* 编辑按钮 */}
      {onChange && <EditButton />}
      
      {/* 无论是否为Slide内容，都使用居中布局 */}
      <div className="flex items-center justify-center h-full w-full" style={{ minHeight: isSlideContent ? '600px' : '400px' }}>
        <div className={isSlideContent ? "w-full max-w-[1280px] aspect-[16/9] relative" : "w-full h-full relative"}>
          <iframe
            ref={iframeRef}
            className="absolute top-0 left-0 w-full h-full border-0"
            title="HTML Preview"
            sandbox="allow-same-origin allow-scripts"
            style={{ margin: '0 auto' }} // 确保内容居中
          />
        </div>
      </div>
    </div>
  );
};

export default HtmlPreview;
