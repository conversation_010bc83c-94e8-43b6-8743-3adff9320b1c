'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useAIStore, CustomModel } from '@/lib/ai-store';
import { ModelProvider } from '@/lib/models';
import { getModelsByProvider } from '@/lib/models';
import * as Select from '@radix-ui/react-select';
import { CheckIcon, ChevronDownIcon } from '@radix-ui/react-icons';

interface ModelSelectorProps {
  className?: string;
  onChange?: (modelId: string) => void;
}

export default function ModelSelector({ className, onChange }: ModelSelectorProps) {
  const { provider, model, customModels, setModel } = useAIStore();

  // 获取当前提供商的模型列表
  const allModels = useMemo(() => {
    const defaultModels = getModelsByProvider(provider, customModels);
    return defaultModels;
  }, [provider, customModels]);

  // 处理模型变更
  const handleModelChange = useCallback((value: string) => {
    setModel(value);
    if (onChange) {
      onChange(value);
    }
  }, [setModel, onChange]);

  // 确保当前选择的模型在列表中
  useEffect(() => {
    const modelExists = allModels.some(m => m.id === model);
    if (!modelExists && allModels.length > 0) {
      handleModelChange(allModels[0].id);
    }
  }, [provider, allModels, handleModelChange, model]);

  return (
    <div className={className}>
      <Select.Root value={model} onValueChange={handleModelChange}>
        <Select.Trigger className="inline-flex w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500">
          <Select.Value />
          <Select.Icon>
            <ChevronDownIcon />
          </Select.Icon>
        </Select.Trigger>
        <Select.Portal>
          <Select.Content className="overflow-hidden rounded-md bg-white shadow-lg">
            <Select.Viewport className="p-1">
              {allModels.map((m) => (
                <Select.Item
                  key={m.id}
                  value={m.id}
                  className="relative flex cursor-default select-none items-center rounded-md px-8 py-2 text-sm text-gray-900 data-[highlighted]:bg-blue-50 data-[highlighted]:text-blue-600"
                >
                  <Select.ItemText>{m.name || m.id}</Select.ItemText>
                  <Select.ItemIndicator className="absolute left-2 inline-flex items-center">
                    <CheckIcon />
                  </Select.ItemIndicator>
                </Select.Item>
              ))}
            </Select.Viewport>
          </Select.Content>
        </Select.Portal>
      </Select.Root>
    </div>
  );
}
