# 对话区域UI改进修复文档

## 修复问题总结

### 问题1：任务执行UI显示详细提示词 ✅
**问题描述**：执行具体任务时，UI上显示了完整的提示词内容，暴露了系统内部逻辑

**修复方案**：
- 创建两个不同的消息：一个用于发送给AI（包含详细提示），一个用于显示给用户（简洁描述）
- 用户看到的消息格式：`执行任务X：[任务描述]`
- AI接收的消息包含完整的执行指令和上下文

### 问题2：任务执行过程中文件检测显示源码 ✅
**问题描述**：任务执行过程中的文件检测没有显示友好的"xxx文件正在生成"信息，而是显示了源码内容

**修复方案**：
- 在任务流式处理器中添加实时文件检测逻辑
- 与普通对话的文件检测保持一致的显示效果
- 显示"正在生成 [文件名]..."而不是源码内容

## 修复详情

### 1. 任务执行消息分离

**修复位置**：`app/content-generator/content-generator-stream.tsx` 第920-934行

**修复前**：
```tsx
// 直接将详细提示词显示给用户
const taskMessage = {
  id: generateUniqueId('msg'),
  role: 'user' as const,
  content: taskExecutionPrompt, // 包含完整提示词
  timestamp: Date.now(),
  type: 'task' as const,
  taskId: currentTask.id
};

setConversation(prev => ({
  ...prev,
  messages: [...prev.messages, taskMessage]
}));
```

**修复后**：
```tsx
// 创建用户友好的任务显示消息
const userFriendlyTaskMessage = {
  id: generateUniqueId('msg'),
  role: 'user' as const,
  content: `执行任务${currentTask.number}：${currentTask.description}`, // 简洁描述
  timestamp: Date.now(),
  type: 'task' as const,
  taskId: currentTask.id
};

// 添加用户友好的任务消息到对话（用于显示）
setConversation(prev => ({
  ...prev,
  messages: [...prev.messages, userFriendlyTaskMessage]
}));

// API调用时使用详细提示词
body: JSON.stringify({
  messages: [...conversation.messages.slice(0, -1), taskMessage].map(msg => ({
    role: msg.role,
    content: msg.content,
  })),
  model,
}),
```

### 2. 任务执行文件检测

**修复位置**：`app/content-generator/content-generator-stream.tsx` 第976-990行

**新增功能**：
```tsx
// 实时检测文件 - 与普通对话一样的检测逻辑
const currentDetectedFiles = extractMultipleFilesFromMessage(content, 'temp-task-id');
if (currentDetectedFiles.length > 0) {
  const fileTypes = currentDetectedFiles.map(file => ({
    type: file.contentType as 'html' | 'markdown',
    filename: file.filename
  }));
  setDetectedFiles(fileTypes);
  
  // 更新流式进度显示
  const fileNames = fileTypes.map(f => f.filename || `${f.type}文件`).join('、');
  setStreamingProgress(`正在生成 ${fileNames}...`);
} else {
  setStreamingProgress(`正在执行任务${currentTask.number}...`);
}
```

### 3. 状态清理优化

**修复位置**：`app/content-generator/content-generator-stream.tsx` 第1004-1006行和第1019-1021行

**新增清理逻辑**：
```tsx
// 在任务完成和错误时清理文件检测状态
setDetectedFiles([]);
setStreamingProgress('');
```

## 用户体验改进

### 修复前的问题

1. **信息暴露**：
   ```
   ⚙️ 现在请执行具体任务，直接生成文件内容：
   【原始需求】写个介绍大模型的PPT
   【当前执行任务】
   任务编号：1
   任务描述：创建封面幻灯片
   【执行要求】
   1. 这是一个具体的执行任务，不是规划任务
   2. 直接生成完整的文件内容，不要重新规划或拆分
   ...
   ```

2. **文件检测异常**：
   ```
   ```html{filename=slide-5-app-scenarios.html}
   正在生成...
   ```

### 修复后的效果

1. **简洁的任务描述**：
   ```
   ⚙️ 执行任务1：创建封面幻灯片
   ```

2. **友好的文件检测**：
   ```
   📄 slide-1-cover.html 文件正在生成...
   ```

## 技术实现亮点

### 1. 消息分离机制
- **显示层**：用户看到简洁的任务描述
- **逻辑层**：AI接收详细的执行指令
- **数据层**：保持对话历史的完整性

### 2. 统一的文件检测
- **一致性**：任务执行和普通对话使用相同的文件检测逻辑
- **实时性**：流式过程中实时检测和显示文件状态
- **友好性**：显示用户友好的进度信息

### 3. 完善的状态管理
- **及时清理**：任务完成后立即清理相关状态
- **错误处理**：异常情况下也能正确清理状态
- **状态同步**：多个状态变量保持同步更新

## 测试验证

### 测试场景1：PPT生成任务

**输入**：`写个介绍大模型的PPT`

**预期效果**：
1. 任务规划正确显示
2. 任务执行时显示：`⚙️ 执行任务1：创建封面幻灯片`
3. 文件检测显示：`📄 slide-1-cover.html 文件正在生成...`
4. 不显示详细的提示词内容

### 测试场景2：多文件生成任务

**输入**：`创建一个完整的网站项目`

**预期效果**：
1. 每个任务执行时显示简洁的任务描述
2. 文件检测正确显示文件名和生成状态
3. 多个文件时显示：`正在生成 index.html、style.css、script.js...`

### 测试场景3：错误处理

**模拟**：网络中断或API错误

**预期效果**：
1. 错误发生时正确清理所有状态
2. 不留下残留的检测状态或进度信息
3. 用户界面恢复到正常状态

## 验证步骤

1. **启动应用**：确保流式内容生成器正常运行
2. **输入测试请求**：使用PPT生成等多任务请求
3. **观察任务显示**：检查任务执行时的消息格式
4. **监控文件检测**：验证文件检测状态显示
5. **检查状态清理**：确认任务完成后状态正确清理

## 成功标准

- ✅ 任务执行时不显示详细提示词
- ✅ 显示简洁的任务描述格式
- ✅ 文件检测显示友好的进度信息
- ✅ 状态清理及时且完整
- ✅ 用户体验流畅自然

## 后续优化建议

1. **进度细化**：为不同类型的文件提供更具体的进度信息
2. **状态持久化**：考虑将任务执行状态保存到本地存储
3. **用户控制**：允许用户选择显示详细信息的级别
4. **动画效果**：为状态切换添加平滑的动画效果
5. **错误提示**：提供更友好的错误信息和恢复建议

## 总结

通过这次修复，我们显著改善了任务执行过程中的用户体验：

1. **隐私保护**：不再暴露系统内部的提示词逻辑
2. **信息清晰**：用户能够清楚地了解当前执行的任务
3. **状态一致**：文件检测在所有场景下都保持一致的显示效果
4. **体验流畅**：状态管理更加完善，避免了界面异常

这些改进使得整个任务执行流程更加专业和用户友好，提升了产品的整体质量。
