'use client';

import { useState, useEffect } from 'react';
import { ModelProvider, ModelConfig } from '@/lib/models';
import { createAIClient, Message } from '@/lib/ai-client';

// 样式常量
const cardStyle = 'bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md';
const inputStyle = 'w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600';
const buttonStyle = 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400';
const labelStyle = 'block text-sm font-medium mb-1 dark:text-gray-300';
const selectStyle = 'w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600';

export default function TestProvidersPage() {
  // 状态
  const [provider, setProvider] = useState<ModelProvider>('openai');
  const [model, setModel] = useState<string>('');
  const [apiKey, setApiKey] = useState<string>('');
  const [baseUrl, setBaseUrl] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [response, setResponse] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [models, setModels] = useState<ModelConfig[]>([]);
  const [logs, setLogs] = useState<string[]>([]);

  // 获取指定provider的模型列表
  useEffect(() => {
    try {
      const client = createAIClient();
      const providerModels = client.getModelsByProvider(provider);
      setModels(providerModels);
      
      if (providerModels.length > 0) {
        setModel(providerModels[0].id);
      } else {
        setModel('');
      }
      
      addLog(`加载了 ${provider} 的 ${providerModels.length} 个模型`);
    } catch (err) {
      setError(`获取模型列表失败: ${err instanceof Error ? err.message : String(err)}`);
      addLog(`错误: 获取模型列表失败 - ${err instanceof Error ? err.message : String(err)}`);
    }
  }, [provider]);

  // 添加日志
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`]);
  };

  // 清除日志
  const clearLogs = () => {
    setLogs([]);
  };

  // 发送测试消息
  const sendTestMessage = async () => {
    if (!apiKey) {
      setError('请输入API密钥');
      return;
    }

    if (!model) {
      setError('请选择模型');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse('');
    
    try {
      addLog(`发送请求到 ${provider} 的 ${model} 模型`);
      
      // 创建AI客户端
      const apiKeys: Record<ModelProvider, string> = {
        openai: '',
        xai: '',
        deepseek: '',
        anthropic: ''
      };
      apiKeys[provider] = apiKey;
      
      const baseUrls: Record<ModelProvider, string> = {
        openai: '',
        xai: '',
        deepseek: '',
        anthropic: ''
      };
      
      if (baseUrl) {
        baseUrls[provider] = baseUrl;
      }
      
      const client = createAIClient({
        apiKeys,
        baseUrls
      });
      
      // 准备消息
      const messages: Message[] = [
        { role: 'user', content: message || '你好，这是一条测试消息。请简短回复。' }
      ];
      
      // 发送请求
      const result = await client.chatCompletion({
        model,
        messages
      });
      
      // 显示响应
      setResponse(result.content);
      addLog(`收到响应: ${result.content.substring(0, 50)}${result.content.length > 50 ? '...' : ''}`);
      if (result.usage) {
        addLog(`Token使用: 提示=${result.usage.promptTokens}, 完成=${result.usage.completionTokens}, 总计=${result.usage.totalTokens}`);
      }
    } catch (err) {
      setError(`请求失败: ${err instanceof Error ? err.message : String(err)}`);
      addLog(`错误: 请求失败 - ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  // 发送流式测试消息
  const sendStreamingTestMessage = async () => {
    if (!apiKey) {
      setError('请输入API密钥');
      return;
    }

    if (!model) {
      setError('请选择模型');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse('');
    
    try {
      addLog(`发送流式请求到 ${provider} 的 ${model} 模型`);
      
      // 创建AI客户端
      const apiKeys: Record<ModelProvider, string> = {
        openai: '',
        xai: '',
        deepseek: '',
        anthropic: ''
      };
      apiKeys[provider] = apiKey;
      
      const baseUrls: Record<ModelProvider, string> = {
        openai: '',
        xai: '',
        deepseek: '',
        anthropic: ''
      };
      
      if (baseUrl) {
        baseUrls[provider] = baseUrl;
      }
      
      const client = createAIClient({
        apiKeys,
        baseUrls
      });
      
      // 准备消息
      const messages: Message[] = [
        { role: 'user', content: message || '你好，这是一条测试消息。请简短回复。' }
      ];
      
      // 发送流式请求
      let streamedResponse = '';
      
      await client.streamingChatCompletion({
        model,
        messages
      }, 
      (content) => {
        // 处理流式内容
        streamedResponse += content;
        setResponse(streamedResponse);
      },
      (error) => {
        // 处理错误
        setError(`流式请求失败: ${error.message}`);
        addLog(`错误: 流式请求失败 - ${error.message}`);
      },
      (finalResponse) => {
        // 处理完成
        addLog(`流式响应完成: ${finalResponse.content.substring(0, 50)}${finalResponse.content.length > 50 ? '...' : ''}`);
        if (finalResponse.usage) {
          addLog(`Token使用: 提示=${finalResponse.usage.promptTokens}, 完成=${finalResponse.usage.completionTokens}, 总计=${finalResponse.usage.totalTokens}`);
        }
      });
    } catch (err) {
      setError(`请求失败: ${err instanceof Error ? err.message : String(err)}`);
      addLog(`错误: 请求失败 - ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Provider系统测试页面</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 配置区域 */}
        <div className={cardStyle}>
          <h2 className="text-xl font-semibold mb-4">配置</h2>
          
          <div className="mb-4">
            <label className={labelStyle}>Provider</label>
            <select 
              className={selectStyle}
              value={provider}
              onChange={(e) => setProvider(e.target.value as ModelProvider)}
            >
              <option value="openai">OpenAI</option>
              <option value="xai">xAI (Grok)</option>
              <option value="deepseek">DeepSeek</option>
              <option value="anthropic">Anthropic (Claude)</option>
            </select>
          </div>
          
          <div className="mb-4">
            <label className={labelStyle}>模型</label>
            <select 
              className={selectStyle}
              value={model}
              onChange={(e) => setModel(e.target.value)}
            >
              {models.length === 0 && <option value="">无可用模型</option>}
              {models.map((m) => (
                <option key={m.id} value={m.id}>
                  {m.id} ({m.maxTokens}tokens)
                </option>
              ))}
            </select>
          </div>
          
          <div className="mb-4">
            <label className={labelStyle}>API密钥</label>
            <input 
              type="password"
              className={inputStyle}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder={`输入${provider} API密钥`}
            />
          </div>
          
          <div className="mb-4">
            <label className={labelStyle}>基础URL (可选)</label>
            <input 
              type="text"
              className={inputStyle}
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              placeholder={`输入${provider}基础URL (可选)`}
            />
          </div>
        </div>
        
        {/* 测试区域 */}
        <div className={cardStyle}>
          <h2 className="text-xl font-semibold mb-4">测试</h2>
          
          <div className="mb-4">
            <label className={labelStyle}>测试消息</label>
            <textarea 
              className={`${inputStyle} h-32`}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="输入测试消息，或留空使用默认消息"
            />
          </div>
          
          <div className="flex space-x-2 mb-6">
            <button 
              className={buttonStyle}
              onClick={sendTestMessage}
              disabled={loading}
            >
              {loading ? '请求中...' : '发送测试请求'}
            </button>
            
            <button 
              className={buttonStyle}
              onClick={sendStreamingTestMessage}
              disabled={loading}
            >
              {loading ? '请求中...' : '发送流式测试请求'}
            </button>
          </div>
          
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}
          
          {response && (
            <div className="mb-4">
              <label className={labelStyle}>响应</label>
              <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded whitespace-pre-wrap">
                {response}
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* 日志区域 */}
      <div className={`${cardStyle} mt-6`}>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">日志</h2>
          <button 
            className="text-sm text-gray-500 hover:text-gray-700"
            onClick={clearLogs}
          >
            清除日志
          </button>
        </div>
        
        <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded h-64 overflow-y-auto font-mono text-sm">
          {logs.length === 0 ? (
            <div className="text-gray-500">暂无日志</div>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="mb-1">
                {log}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
