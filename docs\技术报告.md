## API响应示例

```json
{
  "model": "togethercomputer/llama-2-70b-chat",
  "messages": [
    {"role": "system", "content": "你是一个AI助手"},
    {"role": "user", "content": "你好"}
  ],
  "stream": true,
  "temperature": 0.2,
  "max_tokens": 9000
}
```

## 流式响应处理

```typescript
// 客户端处理流式响应示例
const response = await fetch('/api/get-next-completion-stream-promise', {
  method: 'POST',
  body: JSON.stringify({
    messageId: '123',
    model: 'togethercomputer/llama-2-70b-chat'
  })
});

const reader = response.body.getReader();
while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  console.log(new TextDecoder().decode(value));
}
```
# Easy Coder 技术架构报告

## 核心架构图
```mermaid
graph TD
    A[客户端] -->|POST /api/get-next-completion-stream-promise| B(Edge Runtime)
    B --> C[Prisma+Neon]
    C --> D{Provider工厂}
    D -->|OpenAI Compatible| E[TogetherAI]
    D -->|OpenAI| F[OpenAI]
    E --> G[Helicone监控]
    G --> B
    F --> B
    B -->|流式响应| A
```

## AI集成分析
### 技术栈
- **推理引擎**: 
  - TogetherAI
  - OpenAI
- **数据库**: Prisma + Neon(无服务器PostgreSQL)
- **监控**: Helicone (仅用于OpenAI Compatible)
- **运行时**: Vercel Edge Runtime
- **配置管理**: Cookies + 客户端状态管理

### 关键流程
1. 接收包含messageId和provider的请求
2. 查询关联的完整对话历史
3. 应用消息截断策略(3+7)
4. 根据provider类型，调用相应的AI服务生成流式响应
5. 对于OpenAI Compatible，通过Helicone记录全链路指标

### 性能优化
- 流式传输减少TTFB
- Edge Runtime降低延迟
- 消息截断控制上下文长度
- 温度参数0.2平衡创造力和稳定性
- 动态Provider选择提高灵活性

## 数据库设计
```mermaid
erDiagram
    CHAT ||--o{ MESSAGE : contains
    CHAT {
        string id
        string model
        string provider
        timestamp createdAt
    }
    MESSAGE {
        string id
        string chatId
        string role
        string content
        int position
    }
```

## 大模型Provider设计分析

### 当前架构

项目采用了多Provider架构，支持OpenAI Compatible和OpenAI作为大模型的提供商。以下是项目中大模型相关的主要组件和设计：

#### 1. Provider与模型定义

项目在`lib/constants.ts`中定义了可用的Provider和对应的模型列表：

```typescript
export const PROVIDERS = ["together", "openai"] as const;
export type Provider = (typeof PROVIDERS)[number];

export const PROVIDER_MODELS: Record<Provider, { label: string; value: string }[]> = {
  together: [
    {
      label: "Qwen 2.5 Coder 32B",
      value: "Qwen/Qwen2.5-Coder-32B-Instruct",
    },
    {
      label: "Llama 3.1 405B",
      value: "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
    },
    {
      label: "Llama 3.3 70B",
      value: "meta-llama/Llama-3.3-70B-Instruct-Turbo",
    },
    {
      label: "DeepSeek V3",
      value: "deepseek-ai/DeepSeek-V3",
    },
  ],
  openai: [
    {
      label: "GPT-4",
      value: "gpt-4",
    },
    {
      label: "GPT-3.5 Turbo",
      value: "gpt-3.5-turbo",
    },
  ],
};
```

#### 2. 配置管理

项目使用`AIConfigManager`管理API密钥和Provider设置：

```typescript
// lib/ai-config.ts
export class AIConfigManager {
  private cookies: Cookies;

  constructor(cookies: Cookies) {
    this.cookies = cookies;
  }

  getAPIKey(provider: Provider): string | undefined {
    return this.cookies.get(`${provider}-api-key`);
  }

  setAPIKey(provider: Provider, apiKey: string): void {
    this.cookies.set(`${provider}-api-key`, apiKey, { path: "/" });
  }

  getBaseURL(provider: Provider): string | undefined {
    return this.cookies.get(`${provider}-base-url`);
  }

  setBaseURL(provider: Provider, baseURL: string): void {
    this.cookies.set(`${provider}-base-url`, baseURL, { path: "/" });
  }
}
```

#### 3. 状态管理

使用`useAIStore`钩子管理当前Provider和模型选择：

```typescript
// lib/ai-store.ts
import { create } from "zustand";
import { Provider, PROVIDERS, PROVIDER_MODELS } from "./constants";

type AIStore = {
  currentProvider: Provider;
  currentModel: string;
  setCurrentProvider: (provider: Provider) => void;
  setCurrentModel: (model: string) => void;
};

export const useAIStore = create<AIStore>((set) => ({
  currentProvider: "together",
  currentModel: PROVIDER_MODELS.together[0].value,
  setCurrentProvider: (provider) => {
    set({ currentProvider: provider });
    // 如果当前模型不在新Provider的模型列表中，则选择新Provider的第一个模型
    const modelExists = PROVIDER_MODELS[provider].some(
      (model) => model.value === useAIStore.getState().currentModel
    );
    if (!modelExists) {
      set({ currentModel: PROVIDER_MODELS[provider][0].value });
    }
  },
  setCurrentModel: (model) => set({ currentModel: model }),
}));
```

#### 4. Provider工厂

使用`ProviderFactory`创建不同的Provider实例：

```typescript
// lib/provider-factory.ts
import Together from "together-ai";
import OpenAI from "openai";
import { Provider } from "./constants";
import { AIConfigManager } from "./ai-config";

export class ProviderFactory {
  static createProvider(provider: Provider, configManager: AIConfigManager) {
    switch (provider) {
      case "together":
        let options: ConstructorParameters<typeof Together>[0] = {};
        const togetherApiKey = configManager.getAPIKey("together") || process.env.TOGETHER_API_KEY;
        const togetherBaseURL = configManager.getBaseURL("together");
        
        if (process.env.HELICONE_API_KEY) {
          options.baseURL = "https://together.helicone.ai/v1";
          options.defaultHeaders = {
            "Helicone-Auth": `Bearer ${process.env.HELICONE_API_KEY}`,
            "Helicone-Property-appname": "LlamaCoder",
          };
        } else if (togetherBaseURL) {
          options.baseURL = togetherBaseURL;
        }
        
        if (togetherApiKey) {
          options.apiKey = togetherApiKey;
        }
        
        return new Together(options);
        
      case "openai":
        const openaiApiKey = configManager.getAPIKey("openai") || process.env.OPENAI_API_KEY;
        const openaiBaseURL = configManager.getBaseURL("openai");
        
        if (!openaiApiKey) {
          throw new Error("OpenAI API key is required");
        }
        
        const openaiOptions: any = {
          apiKey: openaiApiKey,
        };
        
        if (openaiBaseURL) {
          openaiOptions.baseURL = openaiBaseURL;
        }
        
        return new OpenAI(openaiOptions);
        
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }
}
```

#### 5. AI设置组件

提供用户界面配置API密钥、Base URL和选择Provider/模型：

```typescript
// components/ai-settings.tsx
export function AISettings() {
  const { currentProvider, currentModel, setCurrentProvider, setCurrentModel } = useAIStore();
  const [togetherApiKey, setTogetherApiKey] = useState("");
  const [togetherBaseURL, setTogetherBaseURL] = useState("");
  const [openaiApiKey, setOpenaiApiKey] = useState("");
  const [openaiBaseURL, setOpenaiBaseURL] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const configManager = new AIConfigManager(new Cookies());
  
  // 加载配置
  useEffect(() => {
    setTogetherApiKey(configManager.getAPIKey("together") || "");
    setTogetherBaseURL(configManager.getBaseURL("together") || "");
    setOpenaiApiKey(configManager.getAPIKey("openai") || "");
    setOpenaiBaseURL(configManager.getBaseURL("openai") || "");
  }, [isOpen]);
  
  // 保存配置
  const saveSettings = () => {
    configManager.setAPIKey("together", togetherApiKey);
    configManager.setBaseURL("together", togetherBaseURL);
    configManager.setAPIKey("openai", openaiApiKey);
    configManager.setBaseURL("openai", openaiBaseURL);
    setIsOpen(false);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon">
          <SettingsIcon className="size-4" />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>AI Settings</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label>Provider</Label>
            <Select
              value={currentProvider}
              onValueChange={(value: Provider) => setCurrentProvider(value)}
            >
              {PROVIDERS.map((provider) => (
                <SelectItem key={provider} value={provider}>
                  {provider.charAt(0).toUpperCase() + provider.slice(1)}
                </SelectItem>
              ))}
            </Select>
          </div>
          
          <div>
            <Label>Model</Label>
            <Select
              value={currentModel}
              onValueChange={(value) => setCurrentModel(value)}
            >
              {PROVIDER_MODELS[currentProvider].map((model) => (
                <SelectItem key={model.value} value={model.value}>
                  {model.label}
                </SelectItem>
              ))}
            </Select>
          </div>
          
          <Tabs defaultValue={currentProvider}>
            <TabsList>
              {PROVIDERS.map((provider) => (
                <TabsTrigger key={provider} value={provider}>
                  {provider.charAt(0).toUpperCase() + provider.slice(1)}
                </TabsTrigger>
              ))}
            </TabsList>
            
            <TabsContent value="together">
              <div className="space-y-2">
                <Label>API Key</Label>
                <Input
                  type="password"
                  value={togetherApiKey}
                  onChange={(e) => setTogetherApiKey(e.target.value)}
                  placeholder="Enter OpenAI Compatible API Key"
                />
                
                <Label>Base URL (Optional)</Label>
                <Input
                  value={togetherBaseURL}
                  onChange={(e) => setTogetherBaseURL(e.target.value)}
                  placeholder="https://api.together.xyz/v1"
                />
              </div>
            </TabsContent>
            
            <TabsContent value="openai">
              <div className="space-y-2">
                <Label>API Key</Label>
                <Input
                  type="password"
                  value={openaiApiKey}
                  onChange={(e) => setOpenaiApiKey(e.target.value)}
                  placeholder="Enter OpenAI API Key"
                />
                
                <Label>Base URL (Optional)</Label>
                <Input
                  value={openaiBaseURL}
                  onChange={(e) => setOpenaiBaseURL(e.target.value)}
                  placeholder="https://api.openai.com/v1"
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter>
          <Button onClick={saveSettings}>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
```

#### 6. API路由更新

更新API路由以支持不同的Provider：

```typescript
// app/api/get-next-completion-stream-promise/route.ts
export async function POST(req: Request) {
  const { messageId, provider = "together", model } = await req.json();
  
  // ...获取消息历史...
  
  const configManager = new AIConfigManager(cookies());
  const providerInstance = ProviderFactory.createProvider(provider, configManager);
  
  let response;
  if (provider === "together") {
    response = await (providerInstance as Together).chat.completions.create({
      model,
      messages: messages.map((m) => ({ role: m.role, content: m.content })),
      stream: true,
      temperature: 0.2,
      max_tokens: 9000,
    });
    
    return new Response(response.toReadableStream());
  } else if (provider === "openai") {
    response = await (providerInstance as OpenAI).chat.completions.create({
      model,
      messages: messages.map((m) => ({ role: m.role, content: m.content })),
      stream: true,
      temperature: 0.2,
      max_tokens: 4096,
    });
    
    return new Response(
      new ReadableStream({
        async start(controller) {
          for await (const chunk of response) {
            const content = chunk.choices[0]?.delta?.content || "";
            if (content) {
              controller.enqueue(new TextEncoder().encode(content));
            }
          }
          controller.close();
        },
      })
    );
  }
  
  throw new Error(`Unsupported provider: ${provider}`);
}
```

### 增加新的Provider类型

要在现有架构中增加新的Provider类型（如Anthropic、Google等），需要执行以下步骤：

#### 1. 更新Provider和模型定义

在`lib/constants.ts`中添加新的Provider和对应的模型：

```typescript
export const PROVIDERS = ["together", "openai", "anthropic"] as const;
export type Provider = (typeof PROVIDERS)[number];

export const PROVIDER_MODELS: Record<Provider, { label: string; value: string }[]> = {
  // 现有Provider的模型...
  anthropic: [
    {
      label: "Claude 3 Opus",
      value: "claude-3-7-sonnet-20250219-20240229",
    },
    {
      label: "Claude 3 Sonnet",
      value: "claude-3-sonnet-20240229",
    },
    {
      label: "Claude 3 Haiku",
      value: "claude-3-haiku-20240307",
    },
  ],
};
```

#### 2. 更新Provider工厂

在`ProviderFactory`中添加新Provider的创建逻辑：

```typescript
static createProvider(provider: Provider, configManager: AIConfigManager) {
  switch (provider) {
    // 现有Provider的逻辑...
    case "anthropic":
      const anthropicApiKey = configManager.getAPIKey("anthropic") || process.env.ANTHROPIC_API_KEY;
      const anthropicBaseURL = configManager.getBaseURL("anthropic");
      
      if (!anthropicApiKey) {
        throw new Error("Anthropic API key is required");
      }
      
      return new Anthropic({
        apiKey: anthropicApiKey,
        baseURL: anthropicBaseURL || undefined,
      });
      
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }
}
```

#### 3. 更新AI设置组件

在`AISettings`组件中添加新Provider的配置界面：

```typescript
<TabsContent value="anthropic">
  <div className="space-y-2">
    <Label>API Key</Label>
    <Input
      type="password"
      value={anthropicApiKey}
      onChange={(e) => setAnthropicApiKey(e.target.value)}
      placeholder="Enter Anthropic API Key"
    />
    
    <Label>Base URL (Optional)</Label>
    <Input
      value={anthropicBaseURL}
      onChange={(e) => setAnthropicBaseURL(e.target.value)}
      placeholder="https://api.anthropic.com"
    />
  </div>
</TabsContent>
```

#### 4. 更新API路由

在API路由中添加新Provider的处理逻辑：

```typescript
else if (provider === "anthropic") {
  response = await (providerInstance as Anthropic).messages.create({
    model,
    messages: messages.map((m) => ({ role: m.role, content: m.content })),
    stream: true,
    temperature: 0.2,
    max_tokens: 4096,
  });
  
  return new Response(
    new ReadableStream({
      async start(controller) {
        for await (const chunk of response) {
          const content = chunk.delta?.text || "";
          if (content) {
            controller.enqueue(new TextEncoder().encode(content));
          }
        }
        controller.close();
      },
    })
  );
}
```

### 实施注意事项

1. **兼容性**：确保现有的聊天会话和功能在添加新Provider后仍然正常工作
2. **错误处理**：为每个Provider添加特定的错误处理逻辑
3. **性能优化**：考虑不同Provider的响应特性，优化流式处理和用户体验
4. **测试**：全面测试所有Provider和模型的组合
5. **文档**：更新文档，说明如何使用不同的Provider和模型
6. **安全性**：确保API密钥安全存储和传输

### 结论

项目已经从单一Provider架构升级为多Provider架构，支持OpenAI Compatible和OpenAI作为大模型提供商。通过抽象Provider工厂、配置管理和状态管理，实现了灵活的Provider切换和配置。这种设计使得添加新的Provider变得相对简单，只需要在几个关键组件中添加相应的逻辑即可。

未来可以考虑进一步优化Provider抽象，例如创建统一的Provider接口，使得不同Provider的实现更加一致和可维护。同时，可以考虑添加更多的配置选项，如模型参数调整、上下文长度控制等，以提供更加灵活和强大的AI体验。