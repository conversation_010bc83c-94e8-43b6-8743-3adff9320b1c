## 任务：以全局aiStore为唯一模型状态源重构
- [x] 1. ContentGeneratorClient移除本地options.model，接入useAIStore
- [x] 2. 所有生成/任务/请求逻辑用aiStore.currentModel
- [x] 3. ConversationPanel等子组件props联动修正（注释说明受控属性）
- [x] 4. 日志与注释完善
- [ ] 5. 测试模型切换与内容生成同步
## 进度跟踪
- 已完成: 4/5
- 下一步: 5. 测试模型切换与内容生成同步
- 预计完成时间: 10分钟
## 注意事项
- 渐进重构，优先类型安全与兼容性
- 保证UI与实际请求模型完全一致
## 🚀 高优先级待排查流式用例失败清单（2025-05-24）

### 失败用例分布
- **文件提取与管理**：回退用例 ✖
- **版本控制**：边界与异常用例 ✖，基础功能用例 ✖
- **UI交互**：边界与异常用例 ✖
- **持久化与协作**：性能用例 ✖
- **回退与降级**：持久化用例 ✖

### 失败用例明细
1. **文件提取与管理 - 回退用例**
2. **版本控制 - 边界与异常用例**
3. **版本控制 - 基础功能用例**
4. **UI交互 - 边界与异常用例**
5. **持久化与协作 - 性能用例**
6. **回退与降级 - 持久化用例**

### 统一排查与修复建议
- 逐一定位上述模块的流式分支实现与用例断言，结合日志与实际交互表现，分析失败原因。
- 检查流式状态同步、回退机制、异常处理、性能瓶颈等关键点。
- 每个用例修复后，补充详细日志与回归验证，确保同类问题不再复现。
- 修复完成后，更新本清单并同步进度。

---