/**
 * 测试模块化提示词系统
 */
import { 
  createDefaultPrompt, 
  createPresentationPrompt, 
  createDataVisualizationPrompt,
  createCustomPrompt,
  promptModuleRegistry
} from './index';

/**
 * 打印提示词
 * @param name 提示词名称
 * @param prompt 提示词内容
 */
function printPrompt(name: string, prompt: string) {
  console.log(`\n===== ${name} =====`);
  console.log(prompt.substring(0, 200) + '...');
  console.log(`长度: ${prompt.length} 字符`);
}

// 打印所有已注册的模块
console.log('已注册的模块:');
promptModuleRegistry.getAllModules().forEach(module => {
  console.log(`- ${module.id}: ${module.name}`);
});

// 测试默认提示词
const defaultPrompt = createDefaultPrompt();
printPrompt('默认提示词', defaultPrompt);

// 测试演示文稿提示词
const presentationPrompt = createPresentationPrompt();
printPrompt('演示文稿提示词', presentationPrompt);


// 测试数据可视化提示词
const dataVisPrompt = createDataVisualizationPrompt();
printPrompt('数据可视化提示词', dataVisPrompt);

// 测试自定义提示词
const customPrompt = createCustomPrompt(
  ['data-visualization', 'presentation-guidelines'],
  ['task-protocol'],
  { customVariable: '自定义值' }
);
printPrompt('自定义提示词', customPrompt);

console.log('\n测试完成!');
