import { NextResponse } from "next/server";
import { ProviderFactory, ProviderType } from "@/lib/provider-factory";

export async function POST(req: Request) {
  try {
    // 解析请求体
    const { 
      apiKey, 
      baseUrl, 
      provider, 
      model,
      simulateNetworkError,
      simulateTimeout,
      simulateRateLimit
    } = await req.json();
    
    // 验证必要参数
    if (!provider || !model) {
      return NextResponse.json(
        { error: "缺少必要参数: provider 或 model" },
        { status: 400 }
      );
    }
    
    // 模拟网络错误
    if (simulateNetworkError) {
      throw new Error("模拟网络错误: 无法连接到API服务器");
    }
    
    // 模拟超时
    if (simulateTimeout) {
      return NextResponse.json(
        { error: "请求超时: API服务器响应时间过长" },
        { status: 408 }
      );
    }
    
    // 模拟速率限制
    if (simulateRateLimit) {
      return NextResponse.json(
        { error: "速率限制: 已超过API调用限制，请稍后再试" },
        { status: 429 }
      );
    }
    
    // 验证API密钥
    if (!apiKey || apiKey === "sk-invalid-key-for-testing") {
      return NextResponse.json(
        { error: "无效的API密钥" },
        { status: 401 }
      );
    }
    
    // 验证基础URL
    if (baseUrl && baseUrl.includes("invalid-api-url")) {
      return NextResponse.json(
        { error: "无效的API基础URL" },
        { status: 400 }
      );
    }
    
    // 获取Provider实例
    try {
      const providerInstance = await ProviderFactory.getProvider({
        type: provider as ProviderType,
        apiKey,
        baseUrl
      });
      
      // 构建消息
      const messages = [
        {
          role: "system",
          content: "You are a helpful assistant.",
        },
        {
          role: "user",
          content: "Hello, how are you today?",
        },
      ];
      
      // 调用API（这里不实际调用，只是验证配置是否有效）
      // 实际测试中，可以使用一个非常短的请求来验证连接是否正常
      
      // 返回成功响应
      return NextResponse.json({
        status: "success",
        message: "错误处理测试通过：配置有效",
      });
    } catch (error) {
      console.error("Provider创建错误:", error);
      return NextResponse.json(
        { error: error instanceof Error ? error.message : "Provider创建失败" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("API调用错误:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "未知错误" },
      { status: 500 }
    );
  }
}

export const maxDuration = 45;
