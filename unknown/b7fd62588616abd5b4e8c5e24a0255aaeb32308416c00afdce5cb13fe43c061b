---
description: 优化需求提示词，使意图和信息更清晰和明确，用于更好地发起提问
---

1.优化输入的需求，使意图和信息更清晰和明确，用于更清晰地表达需求

参考的优化后的案例如下：
<Sample>

我需要帮助构建一个MCP Server，该服务器与Docling Serve交互，将PDF转换为Markdown格式。该服务器应该：

1. 接受本地PDF文件路径或PDF文件URL作为输入
2. 调用Docling Serve API将PDF转换为Markdown
3. 返回转换后的Markdown内容

以下是技术需求：
- 使用TypeScript MCP SDK (mcp)
- 创建一个名为"pdf_to_markdown"的工具，处理转换过程
- 同时支持本地文件路径和网络URL作为PDF源
- 优雅地处理错误，提供信息丰富的错误消息
- 包含适当的日志记录
- 遵循MCP最佳实践进行服务器实现

Docling Serve API端点是：
POST http://localhost:5001/v1alpha/convert/source

对于URL，请求体格式为：

{
  "http_sources": [{"url": "https://example.com/document.pdf"}]
}

对于本地文件，请求体格式为：

{
  "file_sources": [{"path": "/path/to/document.pdf"}]
}


请生成这个MCP服务器的完整实现，包括：
1. 所有必要的导入和依赖项
2. 服务器初始化和配置
3. PDF到Markdown转换工具的实现
4. 错误处理和验证逻辑
5. 运行服务器的主函数
6. 关于如何使用和测试服务器的说明

服务器应该有良好的文档记录，并遵循安全最佳实践，例如在处理之前验证输入路径和URL。
</Sample>