import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { COOKIE_KEYS } from "@/lib/ai-config";
import { ProviderError, ERROR_CODES, createChatCompletion } from "@/lib/provider-factory";

/**
 * 获取API密钥
 */
async function getApiKey(provider: string): Promise<string | undefined> {
  const cookieStore = await cookies();
  const apiKeyKey = `${COOKIE_KEYS.API_KEY_PREFIX}${provider}`;
  return cookieStore.get(apiKeyKey)?.value;
}

/**
 * 获取基础URL
 */
async function getBaseUrl(provider: string): Promise<string | undefined> {
  const cookieStore = await cookies();
  const baseUrlKey = `${COOKIE_KEYS.BASE_URL_PREFIX}${provider}`;
  return cookieStore.get(baseUrlKey)?.value;
}

/**
 * 测试OpenAI API连接
 */
export async function POST(request: NextRequest) {
  try {
    // 获取请求参数
    const body = await request.json();
    const { provider = "openai", model = "gpt-3.5-turbo", testCase } = body;

    // 获取API密钥和基础URL
    const apiKey = await getApiKey(provider);
    const baseUrl = await getBaseUrl(provider);

    // 打印配置信息
    console.log("配置信息", {
      provider,
      hasApiKey: !!apiKey,
      apiKeyType: typeof apiKey,
      hasBaseUrl: !!baseUrl
    });

    // 如果指定了测试用例，模拟错误
    if (testCase) {
      switch (testCase) {
        case "invalid_api_key":
          throw new ProviderError(
            "无效的API密钥: 请检查您的API密钥是否正确",
            ERROR_CODES.INVALID_API_KEY,
            provider as any
          );
        case "invalid_base_url":
          throw new ProviderError(
            "无效的基础URL: 请检查您的基础URL是否正确",
            ERROR_CODES.INVALID_BASE_URL,
            provider as any
          );
        case "network_error":
          throw new ProviderError(
            "网络错误: 无法连接到API服务器，请检查您的网络连接",
            ERROR_CODES.NETWORK_ERROR,
            provider as any
          );
        case "timeout":
          throw new ProviderError(
            "请求超时: API服务器响应时间过长，请稍后再试",
            ERROR_CODES.TIMEOUT,
            provider as any
          );
        case "rate_limit":
          throw new ProviderError(
            "速率限制: 已超过API调用限制，请稍后再试",
            ERROR_CODES.RATE_LIMIT,
            provider as any
          );
      }
    }

    // 准备调用API
    console.log("准备调用API", { provider, model });

    // 创建聊天完成
    const response = await createChatCompletion(
      provider as any,
      { apiKey, baseUrl },
      {
        model,
        messages: [
          { role: "system", content: "你是一个有用的AI助手。" },
          { role: "user", content: "你好，请简单介绍一下自己。" }
        ]
      }
    );

    // 打印响应内容长度
    console.log("API调用成功", { provider, model });
    console.log("响应内容", { responseLength: JSON.stringify(response).length });

    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: "API调用成功",
      data: {
        provider,
        model,
        response
      }
    });
  } catch (error) {
    console.error("API调用错误:", error);

    // 处理不同类型的错误
    if (error instanceof ProviderError) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: error.code,
            message: error.message,
            provider: error.provider
          }
        },
        { status: 400 }
      );
    }

    // 处理其他错误
    return NextResponse.json(
      {
        success: false,
        error: {
          code: "unknown_error",
          message: error instanceof Error ? error.message : "未知错误"
        }
      },
      { status: 500 }
    );
  }
}

export const maxDuration = 45;
