// 模拟API调用
async function mockApiCall(prompt) {
  // 模拟API响应延迟
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // 根据提示返回模拟任务
  if (prompt.includes('HTML')) {
    return {
      tasks: [
        {
          id: 1,
          description: "创建HTML基础结构",
          dependencies: [],
          expectedOutput: "基础HTML模板"
        },
        {
          id: 2,
          description: "添加标题内容",
          dependencies: [1],
          expectedOutput: "带标题的HTML"
        },
        {
          id: 3,
          description: "添加描述内容",
          dependencies: [2],
          expectedOutput: "完整的HTML页面"
        }
      ]
    };
  } else if (prompt.includes('导航菜单')) {
    return {
      tasks: [
        {
          id: 1,
          description: "创建HTML导航结构",
          dependencies: [],
          expectedOutput: "导航HTML结构"
        },
        {
          id: 2,
          description: "添加CSS样式",
          dependencies: [1],
          expectedOutput: "样式定义"
        },
        {
          id: 3,
          description: "添加链接内容",
          dependencies: [1],
          expectedOutput: "链接内容"
        },
        {
          id: 4,
          description: "整合HTML和CSS",
          dependencies: [2, 3],
          expectedOutput: "完整的导航菜单"
        }
      ]
    };
  }
  
  throw new Error('未知的任务类型');
}

// 实现planTasks函数
async function planTasks(userPrompt) {
  try {
    const response = await mockApiCall(userPrompt);
    return response.tasks.map(task => ({
      ...task,
      status: 'pending',
      retryCount: 0
    }));
  } catch (error) {
    console.error('任务规划失败:', error);
    throw error;
  }
}

// 实现executeTask函数
async function executeTask(task, context = []) {
  // 模拟任务执行
  await new Promise(resolve => setTimeout(resolve, 200));
  
  // 模拟失败任务
  if (task.id === 999) {
    throw new Error('模拟任务执行失败');
  }
  
  return `Task ${task.id} output: ${task.expectedOutput}`;
}

// 模拟配置管理器
class MockConfigManager {
  constructor() {
    this.config = {
      model: 'grok-3-beta',
      contextStrategy: 'last',
      executionMode: 'auto',
      maxRetries: 3
    };
  }
}

// 模拟任务测试用例
const TEST_CASES = [
  {
    name: '简单HTML生成测试',
    input: '生成一个简单的HTML页面，包含标题和描述',
    expectedTasks: 3, // 期望拆分成3个任务
  },
  {
    name: '依赖关系测试',
    input: '创建一个带有样式的导航菜单，包含3个链接',
    expectedTasks: 4, // HTML结构 -> CSS样式 -> 链接内容 -> 组合集成
  }
];

// 测试函数
async function runPlannerTests() {
  console.log('=== 任务规划器测试开始 ===\n');
  
  for (const testCase of TEST_CASES) {
    console.log(`测试用例: ${testCase.name}`);
    console.log('输入:', testCase.input);
    
    try {
      // 1. 测试任务规划
      console.log('\n1. 测试任务规划');
      const tasks = await planTasks(testCase.input);
      console.log('规划的任务数量:', tasks.length);
      console.log('任务列表:', JSON.stringify(tasks, null, 2));
      
      // 验证任务数量
      console.assert(
        tasks.length === testCase.expectedTasks,
        `任务数量不符合预期: 期望 ${testCase.expectedTasks}, 实际 ${tasks.length}`
      );
      
      // 2. 测试依赖关系
      console.log('\n2. 测试依赖关系');
      const hasValidDependencies = tasks.every(task => {
        const deps = task.dependencies || [];
        return deps.every(depId => depId < task.id);
      });
      console.log('依赖关系是否有效:', hasValidDependencies ? '✅' : '❌');
      
      // 3. 测试任务执行
      console.log('\n3. 测试任务执行');
      let executedTasks = [];
      for (const task of tasks) {
        console.log(`执行任务 ${task.id}: ${task.description}`);
        try {
          const result = await executeTask(task, getTaskContext(executedTasks));
          executedTasks.push({...task, output: result, status: 'completed'});
          console.log(`任务 ${task.id} 执行成功 ✅`);
        } catch (error) {
          console.log(`任务 ${task.id} 执行失败 ❌:`, error.message);
          break;
        }
      }
      
      // 4. 测试重试机制
      console.log('\n4. 测试重试机制');
      const failingTask = {...tasks[0], id: 999}; // 创建一个会失败的任务
      let retryCount = 0;
      while (retryCount < 3) {
        try {
          await executeTask(failingTask, []);
          break;
        } catch (error) {
          retryCount++;
          console.log(`重试次数: ${retryCount}`);
        }
      }
      console.log('重试机制测试完成');
      
      // 5. 测试上下文策略
      console.log('\n5. 测试上下文策略');
      const contextStrategies = ['none', 'last', 'all'];
      for (const strategy of contextStrategies) {
        console.log(`测试 ${strategy} 策略:`);
        const context = getTaskContext(executedTasks, strategy);
        console.log(`上下文长度: ${context.length}`);
      }
      
    } catch (error) {
      console.error('测试执行失败:', error);
    }
    
    console.log('\n---\n');
  }
  
  console.log('=== 任务规划器测试完成 ===');
}

// 辅助函数：获取任务上下文
function getTaskContext(executedTasks, strategy = 'last') {
  switch (strategy) {
    case 'none':
      return [];
    case 'last':
      return executedTasks.length > 0 ? [executedTasks[executedTasks.length - 1].output] : [];
    case 'all':
      return executedTasks.map(t => t.output);
    default:
      return [];
  }
}

// 运行测试
runPlannerTests().catch(console.error);
