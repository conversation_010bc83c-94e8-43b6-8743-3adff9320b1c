'use client';

import { useAIStore } from '@/lib/ai-store';
import { ModelProvider } from '@/lib/models';
import * as Select from '@radix-ui/react-select';
import { CheckIcon, ChevronDownIcon } from '@radix-ui/react-icons';

// 提供商列表
const PROVIDERS: { label: string; value: ModelProvider }[] = [
  { label: 'OpenAI', value: 'openai' },
  { label: 'xAI (Grok)', value: 'xai' },
  { label: 'DeepSeek', value: 'deepseek' },
  { label: 'Anthropic', value: 'anthropic' },
];

interface ProviderSelectorProps {
  className?: string;
  onChange?: (provider: ModelProvider) => void;
}

export default function ProviderSelector({ className, onChange }: ProviderSelectorProps) {
  const { provider, setProvider } = useAIStore();
  
  // 处理提供商变更
  const handleProviderChange = (value: string) => {
    const newProvider = value as ModelProvider;
    setProvider(newProvider);
    if (onChange) {
      onChange(newProvider);
    }
  };
  
  return (
    <div className={className}>
      <Select.Root value={provider} onValueChange={handleProviderChange}>
        <Select.Trigger className="inline-flex w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500">
          <Select.Value />
          <Select.Icon>
            <ChevronDownIcon />
          </Select.Icon>
        </Select.Trigger>
        <Select.Portal>
          <Select.Content className="overflow-hidden rounded-md bg-white shadow-lg">
            <Select.Viewport className="p-1">
              {PROVIDERS.map((p) => (
                <Select.Item
                  key={p.value}
                  value={p.value}
                  className="relative flex cursor-default select-none items-center rounded-md px-8 py-2 text-sm text-gray-900 data-[highlighted]:bg-blue-50 data-[highlighted]:text-blue-600"
                >
                  <Select.ItemText>{p.label}</Select.ItemText>
                  <Select.ItemIndicator className="absolute left-2 inline-flex items-center">
                    <CheckIcon />
                  </Select.ItemIndicator>
                </Select.Item>
              ))}
            </Select.Viewport>
          </Select.Content>
        </Select.Portal>
      </Select.Root>
    </div>
  );
}
