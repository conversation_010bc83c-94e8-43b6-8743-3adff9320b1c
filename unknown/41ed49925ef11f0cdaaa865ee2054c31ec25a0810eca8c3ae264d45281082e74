[MEMORY BANK: ACTIVE]

### 1. 需求与现状梳理

#### 1.1 目标
- 对 `/content-generator` 页面进行流式输出体验升级，要求：
  - 检测到AI流中“文件写入”信号时，对话区显示进度，右侧文件预览区实时流式渲染代码。
  - 文件写入结束后，预览区自动切换Tab并显示最终内容，对话区展示文件摘要。
  - 若AI输出被maxtoken截断，需基于stop_reason和offset等字段自动续写并拼接，确保内容无重叠、无遗漏。

#### 1.2 现有架构与协议
- 前端基于Next.js + React + TypeScript，UI分为对话区（ConversationPanel）和内容预览区（ContentViewerPanel）。
- AI接口（/api/chat）已支持SSE流式输出，文件写入信号通过event: "file_start"/"file_end"等JSON字段标记，续写时有stop_reason和offset字段。
- 文件预览区目前为整体刷新，尚未实现流式append。

---

### 2. 详细产品与技术需求

#### 2.1 流式输出与UI联动
- **流式解析**：前端需基于SSE/ReadableStream实时消费AI输出，解析event字段，区分普通内容、文件写入开始/结束、token截断等事件。
- **对话区**：检测到event: "file_start"时，显示“正在写入文件...”进度提示；event: "file_end"时，显示文件摘要（如文件名、类型、大小、片段预览）。
- **预览区**：文件写入期间，右侧代码区流式append内容，event: "file_end"后自动切换到“文件预览”Tab，支持高亮、折叠、复制等操作。

#### 2.2 Token截断与内容拼接
- **续写机制**：如event: "stop_reason"为"length"（maxtoken截断），需自动携带offset等字段发起续写请求，拼接内容时严格去重、无遗漏。
- **完整性校验**：每次续写需比对offset、内容hash等，防止重复或断裂，必要时可用内容片段比对辅助。

#### 2.3 状态管理与异常处理
- **状态同步**：对话区与预览区需通过全局store或Context同步流式状态，确保UI一致。
- **异常场景**：如流中断、拼接失败、信号丢失等，需有用户可感知的提示与自动恢复机制。

#### 2.4 体验细节
- **进度动画**：文件写入中有动画/进度条提示。
- **Tab切换**：文件写入完成后自动切换Tab，用户可手动切换历史版本。
- **摘要展示**：文件摘要信息结构化展示，便于用户快速理解生成内容。

---

### 3. 技术实现建议

#### 3.1 流式协议适配
- 封装SSE/ReadableStream解析器，按event类型分发处理。
- 设计FileOperation状态机，驱动UI联动。

#### 3.2 组件改造
- ConversationPanel：增加流式进度与摘要展示逻辑。
- ContentViewerPanel/FileViewer：支持流式append内容，文件写入中与完成后状态切换。

#### 3.3 拼接与校验
- 维护每个文件的offset、hash等元数据，拼接时严格校验。
- 续写请求自动携带offset，后端返回内容需无重叠。

#### 3.4 交互流程（Mermaid时序图）

```mermaid
sequenceDiagram
    participant User
    participant UI
    participant SSEClient
    participant AIBackend

    User->>UI: 触发内容生成
    UI->>SSEClient: 建立SSE流
    AIBackend-->>SSEClient: event: "file_start"
    SSEClient-->>UI: 通知“文件写入中”
    loop 文件流式输出
        AIBackend-->>SSEClient: event: "file_chunk" (offset, content)
        SSEClient-->>UI: 追加内容到预览区
    end
    AIBackend-->>SSEClient: event: "file_end"
    SSEClient-->>UI: 切换Tab并展示摘要
    alt token截断
        AIBackend-->>SSEClient: event: "stop_reason" = "length", offset
        UI->>AIBackend: 自动发起续写（带offset）
        AIBackend-->>SSEClient: event: "file_chunk" (offset, content)
        SSEClient-->>UI: 拼接并继续流式输出
    end
```

---

### 4. 任务分解与优先级

| 优先级 | 任务 | 说明 |
|--------|------|------|
| 🚀 高 | SSE流式协议适配与事件分发 | 解析event: "file_start"/"file_chunk"/"file_end"/"stop_reason"等 |
| 🚀 高 | 文件写入进度与预览区流式append | 预览区支持逐步渲染，UI状态同步 |
| 🚀 高 | Token截断与自动续写拼接 | 基于offset等字段自动续写并拼接 |
| 📋 中 | 对话区进度与摘要展示 | 进度提示与文件摘要结构化展示 |
| 📋 中 | Tab切换与历史版本管理 | 文件写入完成后自动切换Tab，支持版本切换 |
| 📋 中 | 异常与边界场景处理 | 网络/AI中断、拼接失败等容错机制 |
| 📈 低 | 体验优化与动画细节 | 进度动画、代码高亮、交互细节等 |

---

### 5. 风险与注意事项

- ⚠️ 流式事件丢失或顺序错乱需有重试与校验机制。
- ⚠️ 拼接时offset/hash校验必须严谨，防止内容错乱。
- ⚠️ 大文件流式渲染需优化性能，避免前端卡顿。
