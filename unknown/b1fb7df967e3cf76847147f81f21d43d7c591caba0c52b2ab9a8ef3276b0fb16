"use client";

import React, { useState, useEffect } from 'react';
import { CodeDisplayProps } from './types';

// 使用简单版本作为备用，避免水合错误
const SimpleCodeDisplay = ({ content }: { content: string }) => (
  <pre className="p-4 font-mono text-sm whitespace-pre overflow-auto bg-gray-900 text-white h-full">
    <code>{content}</code>
  </pre>
);

const CodeDisplay: React.FC<CodeDisplayProps> = ({
  content,
  language,
  editable = false,
  onChange,
}) => {
  // 客户端状态，用于跟踪组件是否已挂载
  const [isClient, setIsClient] = useState(false);
  const [SyntaxHighlighter, setSyntaxHighlighter] = useState<any>(null);
  const [highlighterStyle, setHighlighterStyle] = useState<any>(null);

  // 在客户端动态导入语法高亮组件
  useEffect(() => {
    const loadHighlighter = async () => {
      const { Prism } = await import('react-syntax-highlighter');
      const { vs } = await import('react-syntax-highlighter/dist/esm/styles/prism');
      setSyntaxHighlighter(() => Prism);
      setHighlighterStyle(vs);
      setIsClient(true);
    };

    loadHighlighter();
  }, []);

  if (editable) {
    return (
      <textarea
        value={content}
        onChange={(e) => onChange?.(e.target.value)}
        className="w-full h-full p-4 font-mono text-sm bg-gray-900 text-white border-none resize-none focus:outline-none"
        spellCheck={false}
        style={{ minHeight: '600px' }}
      />
    );
  }

  // 在客户端渲染语法高亮组件，在服务器端渲染简单版本
  // 检测是否为slide内容
  const isSlideContent = content && (content.includes('<div class="slide"') || content.includes('class="slide'));

  return (
    <div className="w-full h-full overflow-auto bg-gray-900" style={{ minHeight: isSlideContent ? '600px' : '400px' }}>
      {isClient && SyntaxHighlighter ? (
        <SyntaxHighlighter
          language={language}
          style={{
            'hljs': { display: 'block', overflowX: 'auto', padding: '1em', background: '#1a1a1a', color: '#ffffff' },
            'hljs-comment': { color: '#999999', fontStyle: 'italic' },
            'hljs-quote': { color: '#999999', fontStyle: 'italic' },
            'hljs-doctag': { color: '#ff00ff' },
            'hljs-keyword': { color: '#ff00ff' },
            'hljs-formula': { color: '#ff00ff' },
            'hljs-section': { color: '#ff6666' },
            'hljs-name': { color: '#ff6666' },
            'hljs-selector-tag': { color: '#ff6666' },
            'hljs-deletion': { color: '#ff6666' },
            'hljs-subst': { color: '#ff6666' },
            'hljs-literal': { color: '#00ffff' },
            'hljs-string': { color: '#66ff66' },
            'hljs-regexp': { color: '#66ff66' },
            'hljs-addition': { color: '#66ff66' },
            'hljs-attribute': { color: '#66ff66' },
            'hljs-meta-string': { color: '#66ff66' },
            'hljs-built_in': { color: '#ffff00' },
            'hljs-class': { color: '#ffff00' },
            'hljs-title': { color: '#ffff00' },
            'hljs-attr': { color: '#ff9900' },
            'hljs-variable': { color: '#ff9900' },
            'hljs-template-variable': { color: '#ff9900' },
            'hljs-type': { color: '#ff9900' },
            'hljs-selector-class': { color: '#ff9900' },
            'hljs-selector-attr': { color: '#ff9900' },
            'hljs-selector-pseudo': { color: '#ff9900' },
            'hljs-number': { color: '#ff9900' },
            'hljs-symbol': { color: '#00aaff' },
            'hljs-bullet': { color: '#00aaff' },
            'hljs-link': { color: '#00aaff', textDecoration: 'underline' },
            'hljs-meta': { color: '#00aaff' },
            'hljs-selector-id': { color: '#00aaff' },
            'hljs-title.function_': { color: '#00aaff' },
            'hljs-emphasis': { fontStyle: 'italic' },
            'hljs-strong': { fontWeight: 'bold' }
          }}
          showLineNumbers
          customStyle={{
            margin: 0,
            padding: '1rem',
            height: '100%', 
            minHeight: isSlideContent ? '600px' : '400px',
            fontSize: '1rem',
            background: '#1a1a1a',
            borderRadius: 0,
            overflow: 'auto',
            color: '#ffffff',
            lineHeight: '1.5',
            fontFamily: 'Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace',
            width: '100%'
          }}
          lineNumberStyle={{
            color: '#999999',
            paddingRight: '1em',
            textAlign: 'right',
            userSelect: 'none',
            fontSize: '1rem',
            fontWeight: 'bold'
          }}
        >
          {content}
        </SyntaxHighlighter>
      ) : (
        <SimpleCodeDisplay content={content} />
      )}
    </div>
  );
};

export default CodeDisplay;
