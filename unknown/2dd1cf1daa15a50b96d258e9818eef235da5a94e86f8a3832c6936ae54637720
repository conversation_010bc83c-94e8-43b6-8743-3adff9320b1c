"use client";

import { createMessage } from "@/app/(main)/actions";
import LogoSmall from "@/components/icons/logo-small";
import { splitByFirstCodeFence } from "@/lib/utils";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { startTransition, use, useEffect, useRef, useState } from "react";
import ChatBox from "./chat-box";
import ChatLog from "./chat-log";
import CodeViewer from "./code-viewer";
import CodeViewerLayout from "./code-viewer-layout";
import type { Chat } from "./page";
import { Context } from "../../providers";

/**
 * 扩展Chat类型，添加provider属性
 */
interface ExtendedChat extends Chat {
  provider?: string;
}

/**
 * 通用流处理器
 * 处理不同provider的流式响应
 */
class StreamProcessor {
  private fullContent = "";
  private contentHandlers: ((delta: string, content: string) => void)[] = [];
  private finalContentHandlers: ((finalText: string) => void)[] = [];
  private decoder = new TextDecoder();

  /**
   * 处理流式响应
   * @param response Response对象
   * @param provider 提供商类型 
   */
  async processStream(response: Response, provider: string): Promise<void> {
    console.log(`🔍 开始处理${provider}流式响应`);
    
    try {
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error("无法获取响应流读取器");
      }

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        // 解码并处理数据块
        const chunk = this.decoder.decode(value, { stream: true });
        console.log(`🔍 收到数据块:`, chunk.substring(0, 50) + (chunk.length > 50 ? "..." : ""));

        this.fullContent += chunk;
        // 触发content事件
        this.contentHandlers.forEach(handler => handler(chunk, this.fullContent));
      }
      
      console.log(`✅ 流处理完成，最终内容长度: ${this.fullContent.length}`);
      this.finalContentHandlers.forEach(handler => handler(this.fullContent));
    } catch (error) {
      console.error(`❌ 处理${provider}流时出错:`, error);
      throw error;
    }
  }

  /**
   * 注册content事件处理函数
   * @param handler 处理函数
   */
  onContent(handler: (delta: string, content: string) => void): StreamProcessor {
    this.contentHandlers.push(handler);
    return this;
  }

  /**
   * 注册finalContent事件处理函数
   * @param handler 处理函数
   */
  onFinalContent(handler: (finalText: string) => void): StreamProcessor {
    this.finalContentHandlers.push(handler);
    return this;
  }

  /**
   * 获取完整内容
   */
  getFullContent(): string {
    return this.fullContent;
  }

  /**
   * 重置处理器状态
   */
  reset(): void {
    this.fullContent = "";
    this.contentHandlers = [];
    this.finalContentHandlers = [];
  }
}

export default function PageClient({ chat }: { chat: ExtendedChat }) {
  const context = use(Context);
  const [streamPromise, setStreamPromise] = useState<
    Promise<ReadableStream<any>> | undefined
  >(context.streamPromise);
  const [streamText, setStreamText] = useState("");
  const [isShowingCodeViewer, setIsShowingCodeViewer] = useState(
    chat.messages.some((m) => m.role === "assistant"),
  );
  const [activeTab, setActiveTab] = useState<"code" | "preview">("preview");
  const router = useRouter();
  const isHandlingStreamRef = useRef(false);
  const [activeMessage, setActiveMessage] = useState(
    chat.messages.filter((m) => m.role === "assistant").at(-1),
  );

  useEffect(() => {
    async function f() {
      if (!streamPromise || isHandlingStreamRef.current) return;

      isHandlingStreamRef.current = true;
      context.setStreamPromise(undefined);

      try {
        // 获取最新消息的ID
        const latestMessage = chat.messages[chat.messages.length - 1];
        if (!latestMessage) {
          throw new Error("No messages found");
        }

        // 强制使用OpenAI
        const provider = "openai";
        
        console.log("🔍 准备发送请求", {
          messageId: latestMessage.id,
          model: chat.model,
          provider
        });

        const response = await fetch("/api/get-next-completion-stream-promise", {
          method: "POST",
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            messageId: latestMessage.id,
            model: chat.model,
            provider,
          }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        if (!response.body) {
          throw new Error("No response body!");
        }

        let didPushToCode = false;
        let didPushToPreview = false;
        
        console.log("🔍 开始处理流式响应:", { 
          provider,
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries())
        });
        
        // 创建流处理器
        const streamProcessor = new StreamProcessor();
        console.log("🔍 创建StreamProcessor实例");
        
        // 注册事件处理函数
        console.log("🔍 注册事件处理函数");
        streamProcessor
          .onContent((delta, content) => {
            console.log(`🔍 收到content事件，delta长度: ${delta.length}, content长度: ${content.length}`);
            setStreamText((text) => text + delta);
            
            // 检查是否需要显示代码视图
            const parts = splitByFirstCodeFence(content);
            console.log(`🔍 分割后的部分数量: ${parts.length}`);
            
            // 检查是否有代码生成中
            if (!didPushToCode && parts.some(part => part.type === "first-code-fence-generating")) {
              console.log("🔍 检测到代码生成中，切换到代码视图");
              didPushToCode = true;
              setIsShowingCodeViewer(true);
              setActiveTab("code");
            }
            
            // 检查是否有完整代码块
            if (!didPushToPreview && parts.some(part => part.type === "first-code-fence")) {
              console.log("🔍 检测到完整代码块，切换到预览视图");
              didPushToPreview = true;
              setIsShowingCodeViewer(true);
              setActiveTab("preview");
            }
          })
          .onFinalContent(async (finalText) => {
            console.log(`🔍 收到finalContent事件，finalText长度: ${finalText.length}`);
            startTransition(async () => {
              console.log(`🔍 创建消息`);
              const message = await createMessage(
                chat.id,
                finalText,
                "assistant",
              );
              console.log(`🔍 消息创建成功:`, message);

              startTransition(() => {
                console.log(`🔍 更新UI状态`);
                isHandlingStreamRef.current = false;
                setStreamText("");
                setStreamPromise(undefined);
                setActiveMessage(message);
                router.refresh();
              });
            });
          });
        
        // 处理流式响应
        console.log(`🔍 开始处理流式响应`);
        await streamProcessor.processStream(response, provider);
        console.log(`🔍 流式响应处理完成`);
      } catch (error) {
        console.error("❌ 流处理错误:", error);
        isHandlingStreamRef.current = false;
        setStreamPromise(undefined);
      }
    }

    void f();
  }, [chat.id, chat.messages, router, streamPromise, context, chat.model]);

  return (
    <div className="h-dvh">
      <div className="flex h-full">
        <div className="mx-auto flex w-full shrink-0 flex-col overflow-hidden lg:w-1/2">
          <div className="flex items-center gap-4 px-4 py-4">
            <Link href="/">
              <LogoSmall />
            </Link>
            <p className="italic text-gray-500">{chat.title}</p>
          </div>

          <ChatLog
            chat={chat}
            streamText={streamText}
            activeMessage={activeMessage}
            onMessageClick={(message) => {
              if (message !== activeMessage) {
                setActiveMessage(message);
                setIsShowingCodeViewer(true);
              } else {
                setActiveMessage(undefined);
                setIsShowingCodeViewer(false);
              }
            }}
          />

          <ChatBox
            chat={chat}
            onNewStreamPromise={setStreamPromise}
            isStreaming={!!streamPromise}
          />
        </div>

        <CodeViewerLayout
          isShowing={isShowingCodeViewer}
          onClose={() => {
            setActiveMessage(undefined);
            setIsShowingCodeViewer(false);
          }}
        >
          {isShowingCodeViewer && (
            <CodeViewer
              streamText={streamText}
              chat={chat}
              message={activeMessage}
              onMessageChange={setActiveMessage}
              activeTab={activeTab}
              onTabChange={setActiveTab}
              onClose={() => {
                setActiveMessage(undefined);
                setIsShowingCodeViewer(false);
              }}
              onRequestFix={(error: string) => {
                startTransition(async () => {
                  let newMessageText = `The code is not working. Can you fix it? Here's the error:\n\n`;
                  newMessageText += error.trimStart();
                  const message = await createMessage(
                    chat.id,
                    newMessageText,
                    "user",
                  );

                  const streamPromise = fetch(
                    "/api/get-next-completion-stream-promise",
                    {
                      method: "POST",
                      headers: {
                        'Content-Type': 'application/json'
                      },
                      body: JSON.stringify({
                        messageId: message.id,
                        model: chat.model,
                        provider: "openai", // 强制使用OpenAI
                      }),
                    },
                  ).then((res) => {
                    if (!res.ok) {
                      throw new Error(`HTTP error! status: ${res.status}`);
                    }
                    return res.body as ReadableStream<any>;
                  });
                  setStreamPromise(streamPromise);
                  router.refresh();
                });
              }}
            />
          )}
        </CodeViewerLayout>
      </div>
    </div>
  );
}
