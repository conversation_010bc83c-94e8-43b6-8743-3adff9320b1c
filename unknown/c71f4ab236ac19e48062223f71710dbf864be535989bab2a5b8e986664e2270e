import { OpenAI } from "openai";
import { CustomModel, Endpoint } from './constants';

// 使用动态导入，避免循环依赖问题
// 定义Together类型和导入函数
let Together: any = null;

// 动态导入together-ai库的函数
async function importTogether() {
  if (Together === null) {
    try {
      console.log("🔍 开始动态导入together-ai库...");
      const togetherModule = await import("together-ai");
      Together = togetherModule.default;
      console.log("✅ together-ai库动态导入成功");
    } catch (error) {
      console.error("❌ together-ai库动态导入失败:", error);
    }
  }
}

export type ProviderType = "together" | "openai" | "anthropic" | "google" | "alicloud" | "tencent" | "volcanoengine";

export interface ProviderConfig {
  type: ProviderType;
  apiKey?: string;
  baseUrl?: string;
  customModels?: CustomModel[];
  endpoints?: Endpoint[];
  model?: string;
}

/**
 * 扩展配置接口
 */
export interface ExtendedProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  heliconeApiKey?: string;
  chatId?: string;
  customModels?: CustomModel[];
  endpoints?: Endpoint[];
  model?: string;
}

// 默认配置
const DEFAULT_CONFIGS: Record<ProviderType, Omit<ProviderConfig, "type">> = {
  together: {},
  openai: {
    baseUrl: "https://api.openai.com/v1"
  },
  anthropic: {
    baseUrl: "https://api.anthropic.com"
  },
  google: {
    baseUrl: "https://generativelanguage.googleapis.com"
  },
  alicloud: {
    baseUrl: "https://dashscope.aliyuncs.com"
  },
  tencent: {
    baseUrl: "https://hunyuan.cloud.tencent.com"
  },
  volcanoengine: {
    baseUrl: "https://volc-open.volcengine.com"
  }
};

/**
 * 自定义错误类
 */
export class ProviderError extends Error {
  code: string;
  provider: ProviderType;
  details?: any;

  constructor(message: string, code: string, provider: ProviderType, details?: any) {
    super(message);
    this.name = "ProviderError";
    this.code = code;
    this.provider = provider;
    this.details = details;
  }
}

/**
 * 错误代码常量
 */
export const ERROR_CODES = {
  INVALID_API_KEY: "invalid_api_key",
  INVALID_BASE_URL: "invalid_base_url",
  NETWORK_ERROR: "network_error",
  TIMEOUT: "timeout",
  RATE_LIMIT: "rate_limit",
  PROVIDER_NOT_SUPPORTED: "provider_not_supported",
  UNKNOWN: "unknown_error"
};

/**
 * 解析API错误
 */
function parseApiError(error: any, provider: ProviderType): ProviderError {
  console.error(`解析${provider}错误:`, error);

  // 默认错误
  let code = ERROR_CODES.UNKNOWN;
  let message = `${provider}调用失败: ${error.message || "未知错误"}`;

  // 检查错误类型
  if (error.code === "ERR_INVALID_URL") {
    code = ERROR_CODES.INVALID_BASE_URL;
    message = `无效的基础URL: 请检查${provider}的基础URL配置`;
  } else if (error.status === 401 || (error.message && error.message.includes("auth") || error.message && error.message.includes("key"))) {
    code = ERROR_CODES.INVALID_API_KEY;
    message = `无效的API密钥: 请检查${provider}的API密钥配置`;
  } else if (error.code === "ECONNREFUSED" || error.code === "ENOTFOUND" || error.message && error.message.includes("connect")) {
    code = ERROR_CODES.NETWORK_ERROR;
    message = `网络错误: 无法连接到${provider}服务器，请检查网络连接`;
  } else if (error.code === "ETIMEDOUT" || error.message && error.message.includes("timeout")) {
    code = ERROR_CODES.TIMEOUT;
    message = `请求超时: ${provider}服务器响应时间过长，请稍后再试`;
  } else if (error.status === 429 || (error.message && error.message.includes("rate") || error.message && error.message.includes("limit"))) {
    code = ERROR_CODES.RATE_LIMIT;
    message = `速率限制: 已超过${provider}的API调用限制，请稍后再试`;
  }

  // 修复错误消息与错误代码不一致问题
  if (code === ERROR_CODES.INVALID_API_KEY) {
    message = `无效的API密钥: 请检查${provider}的API密钥配置`;
  } else if (code === ERROR_CODES.INVALID_BASE_URL) {
    message = `无效的基础URL: 请检查${provider}的基础URL配置`;
  } else if (code === ERROR_CODES.NETWORK_ERROR) {
    message = `网络错误: 无法连接到${provider}服务器，请检查网络连接`;
  } else if (code === ERROR_CODES.TIMEOUT) {
    message = `请求超时: ${provider}服务器响应时间过长，请稍后再试`;
  } else if (code === ERROR_CODES.RATE_LIMIT) {
    message = `速率限制: 已超过${provider}的API调用限制，请稍后再试`;
  }

  return new ProviderError(message, code, provider, error);
}

export class ProviderFactory {
  static async getProvider(config: ProviderConfig) {
    const factory = new ProviderFactory();
    return await factory.createProvider(config.type, config);
  }

  // 实例方法，创建Provider实例
  async createProvider(type: ProviderType, config: ExtendedProviderConfig) {
    console.log(`创建提供商实例: ${type}`, { 
      hasApiKey: !!config.apiKey, 
      hasBaseUrl: !!config.baseUrl,
      hasCustomModels: !!config.customModels?.length,
      hasEndpoints: !!config.endpoints?.length
    });

    try {
      switch (type) {
        case "together":
          return await this.createTogetherProvider(config);
        case "openai":
          return await this.createOpenAIProvider(config);
        // 其他提供商...
        default:
          throw new ProviderError(
            `不支持的提供商类型: ${type}`,
            ERROR_CODES.PROVIDER_NOT_SUPPORTED,
            type as ProviderType
          );
      }
    } catch (error) {
      if (error instanceof ProviderError) {
        throw error;
      }
      throw parseApiError(error, type);
    }
  }

  private async createTogetherProvider(config: ExtendedProviderConfig) {
    try {
      // 动态导入together-ai库
      await importTogether();
      
      // 检查Together是否成功导入
      if (!Together) {
        console.error("❌ Together库未成功导入");
        throw new ProviderError(
          "Together库未成功导入，请检查依赖是否正确安装",
          ERROR_CODES.PROVIDER_NOT_SUPPORTED,
          "together"
        );
      }
      const defaultConfig = DEFAULT_CONFIGS.together;

      // 显式定义options类型，避免TypeScript错误
      // 使用any类型暂时绕过TypeScript错误
      const options: any = {
        apiKey: config.apiKey || process.env.TOGETHER_API_KEY || "",
        baseURL: defaultConfig.baseUrl || "https://api.together.xyz"
      };
      
      // 添加Helicone支持
      if (process.env.HELICONE_API_KEY) {
        (options as any).defaultHeaders = {
          "Helicone-Auth": `Bearer ${process.env.HELICONE_API_KEY}`
        };
        
        // 添加会话信息
        if (config.chatId) {
          (options as any).defaultHeaders["Helicone-Session-Id"] = config.chatId;
          (options as any).defaultHeaders["Helicone-Session-Name"] = "LlamaCoder Chat";
        }
      }
      
      // 使用自定义API密钥（如果提供）
      if (config.apiKey) {
        options.apiKey = config.apiKey as string;
      }
      
      // 使用自定义Base URL（如果提供）
      if (config.baseUrl) {
        if (typeof config.baseUrl === 'object' && config.baseUrl !== null) {
          // 检查是否有value属性
          if (config.baseUrl && typeof config.baseUrl === 'object' && 'value' in config.baseUrl) {
            console.log("Together baseUrl是对象类型，提取value属性", config.baseUrl);
            options.baseURL = (config.baseUrl as any).value;
          } else {
            console.log("Together baseUrl是对象类型，尝试转换为字符串", config.baseUrl);
            options.baseURL = String(config.baseUrl);
          } 
        } else if (typeof config.baseUrl === 'string') {
          options.baseURL = config.baseUrl;
        }
      }
      
      // 验证API密钥
      if (!options.apiKey) {
        throw new ProviderError(
          "Together API密钥未提供",
          ERROR_CODES.INVALID_API_KEY,
          "together"
        );
      }
      
      // 验证Base URL
      if (!options.baseURL) {
        throw new ProviderError(
          "Together 基础URL未提供",
          ERROR_CODES.INVALID_BASE_URL,
          "together"
        );
      }
      
      console.log("🔍 准备创建Together提供商实例", { 
        hasApiKey: !!options.apiKey, 
        baseURL: options.baseURL 
      });
      
      try {
        console.log("🔍 开始创建Together实例...");
        const instance = new Together(options);
        console.log("✅ Together实例创建成功", { instanceType: typeof instance });
        return instance;
      } catch (error) {
        console.error("❌ Together实例创建失败:", error);
        throw error;
      }
    } catch (error) {
      console.error("创建Together提供商实例失败", error);
      if (error instanceof ProviderError) {
        throw error;
      }
      throw parseApiError(error, "together");
    }
  }

  private async createOpenAIProvider(config: ExtendedProviderConfig) {
    try {
      const defaultConfig = DEFAULT_CONFIGS.openai;
      
      // 处理API密钥 
      let apiKey: string = "";
      if (config.apiKey) {
        // 确保apiKey是字符串类型
        if (typeof config.apiKey === 'object' && config.apiKey !== null) {
          // 检查是否有value属性
          if ('value' in config.apiKey) {
            console.log("OpenAI API密钥是对象类型，提取value属性", config.apiKey); 
            apiKey = (config.apiKey as any).value;
          } else {
            console.log("OpenAI API密钥是对象类型，尝试转换为字符串", config.apiKey);
            apiKey = String(config.apiKey);
          }
        } else {
          apiKey = String(config.apiKey);
        }
      } else if (process.env.OPENAI_API_KEY) {
        apiKey = process.env.OPENAI_API_KEY;
      }
      
      // 验证API密钥
      if (!apiKey) {
        throw new ProviderError(
          "OpenAI API密钥未提供",
          ERROR_CODES.INVALID_API_KEY,
          "openai"
        );
      }
      
      if (typeof apiKey !== 'string') {
        console.error("OpenAI API密钥类型错误，期望字符串类型", typeof apiKey);
        apiKey = String(apiKey);
      }
      
      // 注意：OpenAI SDK使用baseURL（大写URL）
      const options: ConstructorParameters<typeof OpenAI>[0] = {
        apiKey: apiKey,
      };
      
      // 处理Base URL
      let baseUrl: string | undefined = undefined;
      
      // 检查是否有匹配的自定义端点
      if (config.endpoints?.length && config.customModels?.length && config.model) {
        const model = config.customModels.find(m => m.value === config.model);
        if (model?.endpoint) {
          const endpoint = config.endpoints.find(e => e.name === model.endpoint);
          if (endpoint) {
            console.log(`🔍 找到匹配的自定义端点: ${endpoint.name}`);
            baseUrl = endpoint.baseUrl;
            // 如果端点有自己的API密钥，使用端点的API密钥
            if (endpoint.apiKey) {
              apiKey = endpoint.apiKey;
              options.apiKey = apiKey;
            }
          }
        }
      }
      
      // 如果没有找到匹配的自定义端点，使用配置中的baseUrl或默认值
      if (!baseUrl) {
        if (config.baseUrl) {
          if (typeof config.baseUrl === 'object' && config.baseUrl !== null) {
            // 检查是否有value属性
            if ('value' in config.baseUrl) {
              console.log("OpenAI baseUrl是对象类型，提取value属性", config.baseUrl);
              baseUrl = (config.baseUrl as any).value;
            } else {
              console.log("OpenAI baseUrl是对象类型，尝试转换为字符串", config.baseUrl);
              baseUrl = String(config.baseUrl);
            }
          } else if (typeof config.baseUrl === 'string') {
            baseUrl = config.baseUrl;
          }
        } else if (defaultConfig.baseUrl) {
          baseUrl = defaultConfig.baseUrl;
        }
      }
      
      if (baseUrl) {
        options.baseURL = baseUrl;
      }
      
      // 验证Base URL
      if (!options.baseURL) {
        throw new ProviderError(
          "OpenAI 基础URL未提供",
          ERROR_CODES.INVALID_BASE_URL,
          "openai"
        );
      }
      
      // 验证Base URL格式
      try {
        new URL(options.baseURL);
      } catch (error) {
        throw new ProviderError(
          `OpenAI 基础URL格式无效: ${options.baseURL}`,
          ERROR_CODES.INVALID_BASE_URL,
          "openai",
          error
        );
      }
      
      console.log("创建OpenAI提供商实例", { 
        hasApiKey: !!options.apiKey, 
        apiKeyType: typeof options.apiKey,
        baseURL: options.baseURL 
      });
      
      return new OpenAI(options);
    } catch (error) {
      console.error("创建OpenAI提供商实例失败", error);
      if (error instanceof ProviderError) {
        throw error;
      }
      throw parseApiError(error, "openai");
    }
  }

  async getTogetherProvider(config: ProviderConfig) {
    return this.createTogetherProvider(config);
  }

  async getOpenAIProvider(config: ProviderConfig) {
    return this.createOpenAIProvider(config);
  }
}
 
/**
 * 统一的聊天完成接口
 */
export interface ChatCompletionOptions {
  model: string;
  messages: Array<{ role: string; content: string | Array<any> }>;
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  sessionId?: string;
  sessionName?: string;
}

/**
 * 统一的聊天完成方法
 */
export async function createChatCompletion( 
  provider: ProviderType,
  config: Omit<ProviderConfig, "type">,
  options: ChatCompletionOptions
) {
  console.log(`开始创建聊天完成: ${provider}`, { 
    model: options.model, 
    hasApiKey: !!config.apiKey, 
    hasBaseUrl: !!config.baseUrl,
    hasCustomModels: !!config.customModels?.length,
    hasEndpoints: !!config.endpoints?.length
  });

  try {
    const factory = new ProviderFactory();
    const instance = await factory.createProvider(provider, {
      ...config,
      model: options.model // 传递当前使用的模型名称
    });

    console.log(`调用API: ${provider}`, { model: options.model });

    switch (provider) {
      case "together":
        return await instance.chat.completions.create({
          model: options.model,
          messages: options.messages,
          stream: options.stream,
          temperature: options.temperature,
          max_tokens: options.max_tokens
        });
      case "openai":
        return await instance.chat.completions.create({
          model: options.model,
          messages: options.messages,
          stream: options.stream,
          temperature: options.temperature,
          max_tokens: options.max_tokens
        });
      default:
        throw new ProviderError(
          `不支持的提供商类型: ${provider}`,
          ERROR_CODES.PROVIDER_NOT_SUPPORTED,
          provider
        );
    }
  } catch (error) {
    console.error(`${provider} API调用失败`, error);
    
    if (error instanceof ProviderError) {
      throw error;
    }
    
    throw parseApiError(error, provider);
  }
}
