export type ContentType = 'html' | 'markdown';
export type ViewMode = 'code' | 'preview' | 'split';

export interface ContentViewerProps {
  // 内容
  content: string;
  // 内容类型
  contentType: ContentType;
  // 初始视图模式
  initialViewMode?: ViewMode;
  // 是否可编辑
  editable?: boolean;
  // 主题
  theme?: 'light' | 'dark';
  // 分屏比例 (0-100)
  splitRatio?: number;
  // 是否隐藏工具栏
  hideToolbar?: boolean;
  // 事件回调
  onChange?: (content: string) => void;
  onContentTypeChange?: (type: ContentType) => void;
  onViewModeChange?: (mode: ViewMode) => void;
}

export interface ToolbarProps {
  contentType: ContentType;
  viewMode: ViewMode;
  isEditing?: boolean;
  canEdit?: boolean;
  onContentTypeChange: (type: ContentType) => void;
  onViewModeChange: (mode: ViewMode) => void;
  onEditToggle?: () => void;
}

export interface CodeDisplayProps {
  content: string;
  language: string;
  editable?: boolean;
  onChange?: (content: string) => void;
}

export interface HtmlPreviewProps {
  content: string;
  onChange?: (content: string) => void;
}

export interface MarkdownPreviewProps {
  content: string;
}

export interface StatusBarProps {
  contentType: ContentType;
  lineCount: number;
  encoding: string;
  onCopyCode: () => void;
  onRefresh: () => void;
}
