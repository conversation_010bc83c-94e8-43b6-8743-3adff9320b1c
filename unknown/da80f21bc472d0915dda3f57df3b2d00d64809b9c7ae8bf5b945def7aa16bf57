"use client";

import React, { useState } from 'react';
import dynamic from 'next/dynamic';
import { ContentType, ViewMode } from '@/components/content-viewer/types';

// 使用动态导入加载ContentViewer组件，并禁用SSR
const ContentViewer = dynamic(
  () => import('@/components/content-viewer/content-viewer'),
  { ssr: false }
);

export default function TestContentViewerPage() {
  const [contentType, setContentType] = useState<ContentType>('html');
  const [viewMode, setViewMode] = useState<ViewMode>('split');
  const [content, setContent] = useState<string>('');
  const [editHistory, setEditHistory] = useState<string[]>([]);

  // 初始化内容
  const initialHtmlExample = `<!DOCTYPE html>
<html>
<head>
  <title>HTML内容查看器示例</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      color: #333;
      background-color: #f9f9f9;
    }
    h1 {
      color: #2563eb;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 10px;
      text-align: center;
    }
    h2 {
      color: #4b5563;
      margin-top: 1.5em;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;
    }
    .card {
      flex: 1;
      min-width: 200px;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      background-color: white;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    .card h3 {
      margin-top: 0;
      color: #4b5563;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }
    .highlight {
      background-color: #ffffcc;
      padding: 2px 5px;
      border-radius: 3px;
    }
    .demo-table {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
    }
    .demo-table th, .demo-table td {
      border: 1px solid #e5e7eb;
      padding: 8px 12px;
      text-align: left;
    }
    .demo-table th {
      background-color: #f3f4f6;
    }
    .demo-table tr:nth-child(even) {
      background-color: #f9fafb;
    }
    .demo-form {
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      margin: 20px 0;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .form-group input, .form-group textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .btn {
      background-color: #2563eb;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    .btn:hover {
      background-color: #1d4ed8;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6b7280;
      font-size: 0.9em;
      border-top: 1px solid #e5e7eb;
      padding-top: 20px;
    }
  </style>
</head>
<body>
  <h1>HTML内容查看器示例</h1>

  <p>这是一个展示<span class="highlight">HTML内容查看器</span>功能的示例页面。它包含了各种HTML元素和CSS样式，用于测试渲染效果。</p>

  <h2>功能特点</h2>

  <div class="container">
    <div class="card">
      <h3>代码高亮</h3>
      <p>支持HTML代码的语法高亮显示，使代码更易读。</p>
    </div>
    <div class="card">
      <h3>实时预览</h3>
      <p>实时渲染HTML内容，显示最终效果。</p>
    </div>
    <div class="card">
      <h3>分屏模式</h3>
      <p>同时查看代码和预览，并支持调整分割比例。</p>
    </div>
  </div>

  <h2>数据表格示例</h2>

  <table class="demo-table">
    <thead>
      <tr>
        <th>功能</th>
        <th>描述</th>
        <th>支持状态</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>HTML渲染</td>
        <td>支持完整的HTML标签和CSS样式</td>
        <td>✅ 已支持</td>
      </tr>
      <tr>
        <td>代码高亮</td>
        <td>支持HTML代码语法高亮</td>
        <td>✅ 已支持</td>
      </tr>
      <tr>
        <td>分屏模式</td>
        <td>同时显示代码和预览</td>
        <td>✅ 已支持</td>
      </tr>
    </tbody>
  </table>

  <h2>表单示例</h2>

  <div class="demo-form">
    <div class="form-group">
      <label for="name">姓名</label>
      <input type="text" id="name" placeholder="请输入您的姓名">
    </div>

    <div class="form-group">
      <label for="email">电子邮箱</label>
      <input type="email" id="email" placeholder="请输入您的电子邮箱">
    </div>

    <div class="form-group">
      <label for="message">留言</label>
      <textarea id="message" rows="4" placeholder="请输入您的留言"></textarea>
    </div>

    <button class="btn">提交</button>
  </div>

  <footer>
    <p>&copy; 2023 HTML内容查看器示例 | 所有内容仅用于测试</p>
  </footer>
</body>
</html>`;

  const initialMarkdownExample = `# Markdown示例文档

这是一个**Markdown**示例文档，用于展示Markdown的基本语法和渲染效果。

## 标题和段落

Markdown支持多级标题和段落文本。段落之间需要有空行分隔。

## 文本格式化

Markdown支持各种文本格式化：

- **粗体文本**
- *斜体文本*
- ~~删除线文本~~
- \`行内代码\`

## 列表

### 无序列表

- 项目一
- 项目二
- 项目三

### 有序列表

1. 第一步
2. 第二步
3. 第三步

## 代码块

\`\`\`javascript
function greeting(name) {
  return \`Hello, \${name}!\`;
}

console.log(greeting('World'));
\`\`\`

## 表格

| 名称 | 类型 | 描述 |
|------|------|------|
| id | string | 唯一标识符 |
| name | string | 名称 |
| age | number | 年龄 |

## 链接和图片

[Markdown指南](https://www.markdownguide.org/)

![示例图片](https://via.placeholder.com/150)

## 引用

> Markdown是一种轻量级标记语言，创建于2004年，现在已经成为世界上最流行的标记语言之一。

## 水平线

---

这就是Markdown的基本语法示例。`;

  // 初始化内容
  React.useEffect(() => {
    setContent(contentType === 'html' ? initialHtmlExample : initialMarkdownExample);
  }, [contentType, initialHtmlExample, initialMarkdownExample]);

  // 处理内容变化
  const handleContentChange = (newContent: string) => {
    // 保存编辑历史
    setEditHistory(prev => [...prev, content]);
    // 更新当前内容
    setContent(newContent);
  };

  // 处理内容类型变化
  const handleContentTypeChange = (type: ContentType) => {
    setContentType(type);
    // 如果切换内容类型，重置内容
    if (type !== contentType) {
      setContent(type === 'html' ? initialHtmlExample : initialMarkdownExample);
      setEditHistory([]);
    }
  };

  // 撤销编辑
  const handleUndo = () => {
    if (editHistory.length > 0) {
      const previousContent = editHistory[editHistory.length - 1];
      setContent(previousContent);
      setEditHistory(prev => prev.slice(0, -1));
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">内容查看器示例 (支持编辑)</h1>

      {/* 内容类型切换按钮 */}
      <div className="flex space-x-4 mb-4">
        <button
          className={`px-4 py-2 rounded-md ${contentType === 'html' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => handleContentTypeChange('html')}
        >
          HTML示例
        </button>
        <button
          className={`px-4 py-2 rounded-md ${contentType === 'markdown' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => handleContentTypeChange('markdown')}
        >
          Markdown示例
        </button>
      </div>

      {/* 视图模式切换按钮 */}
      <div className="flex space-x-4 mb-4">
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'code' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('code')}
        >
          仅代码
        </button>
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'preview' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('preview')}
        >
          仅预览
        </button>
        <button
          className={`px-4 py-2 rounded-md ${viewMode === 'split' ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-800'}`}
          onClick={() => setViewMode('split')}
        >
          分屏模式
        </button>
      </div>

      {/* 当前内容类型和视图模式信息 */}
      <div className="mb-4 p-3 bg-gray-100 rounded-md">
        <div className="flex justify-between items-center">
          <p className="text-gray-700">
            当前内容类型: <span className="font-semibold">{contentType === 'html' ? 'HTML' : 'Markdown'}</span> |
            当前视图模式: <span className="font-semibold">
              {viewMode === 'code' ? '仅代码' : viewMode === 'preview' ? '仅预览' : '分屏模式'}
            </span>
          </p>
          <div className="flex space-x-2">
            <button 
              onClick={handleUndo} 
              disabled={editHistory.length === 0}
              className={`px-3 py-1 rounded text-sm ${editHistory.length === 0 ? 'bg-gray-300 text-gray-500 cursor-not-allowed' : 'bg-yellow-500 text-white hover:bg-yellow-600'}`}
            >
              撤销编辑
            </button>
            <button 
              onClick={() => {
                setContent(contentType === 'html' ? initialHtmlExample : initialMarkdownExample);
                setEditHistory([]);
              }}
              className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
            >
              重置内容
            </button>
          </div>
        </div>
        {editHistory.length > 0 && (
          <div className="mt-2 text-sm text-gray-600">
            已编辑 {editHistory.length} 次 | 点击预览区域右上角的编辑按钮可继续编辑
          </div>
        )}
      </div>

      <div className="h-[600px] border rounded-lg overflow-hidden">
        <ContentViewer
          content={content || (contentType === 'html' ? initialHtmlExample : initialMarkdownExample)}
          contentType={contentType}
          initialViewMode={viewMode}
          editable={true}
          onContentTypeChange={handleContentTypeChange}
          onViewModeChange={setViewMode}
          onChange={handleContentChange}
        />
      </div>
    </div>
  );
}
