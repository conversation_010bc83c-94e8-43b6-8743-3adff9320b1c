// 配置管理测试脚本
// 这个脚本用于测试AI配置管理功能

// 模拟cookies存储
class MockCookies {
  constructor() {
    this.cookies = {};
  }

  get(key) {
    return this.cookies[key];
  }

  set(key, value) {
    this.cookies[key] = value;
  }

  remove(key) {
    delete this.cookies[key];
  }
}

// 导入AIConfigManager
const { AIConfigManager } = require('../lib/ai-config');

// 测试函数
function runTests() {
  console.log('=== 配置管理测试开始 ===');
  
  // 创建模拟cookies
  const cookies = new MockCookies();
  
  // 创建配置管理器
  const configManager = new AIConfigManager(cookies);
  
  // 测试1: 保存API Key
  console.log('\n测试1: 保存API Key');
  configManager.saveConfig({
    provider: 'openai',
    apiKey: 'test-api-key'
  });
  
  const savedApiKey = cookies.get('ai_api_key_openai');
  console.log(`保存的API Key: ${savedApiKey}`);
  console.log(`测试结果: ${savedApiKey === 'test-api-key' ? '通过 ✅' : '失败 ❌'}`);
  
  // 测试2: 保存Base URL
  console.log('\n测试2: 保存Base URL');
  configManager.saveConfig({
    provider: 'openai',
    baseUrl: 'https://test-api.openai.com/v1'
  });
  
  const savedBaseUrl = cookies.get('ai_base_url_openai');
  console.log(`保存的Base URL: ${savedBaseUrl}`);
  console.log(`测试结果: ${savedBaseUrl === 'https://test-api.openai.com/v1' ? '通过 ✅' : '失败 ❌'}`);
  
  // 测试3: 加载配置
  console.log('\n测试3: 加载配置');
  const config = configManager.getConfig();
  console.log('加载的配置:', config);
  console.log(`测试结果: ${
    config.provider === 'openai' && 
    config.apiKey === 'test-api-key' && 
    config.baseUrl === 'https://test-api.openai.com/v1' 
      ? '通过 ✅' 
      : '失败 ❌'
  }`);
  
  // 测试4: 获取特定提供商的API Key
  console.log('\n测试4: 获取特定提供商的API Key');
  const openaiApiKey = configManager.getApiKey('openai');
  console.log(`OpenAI API Key: ${openaiApiKey}`);
  console.log(`测试结果: ${openaiApiKey === 'test-api-key' ? '通过 ✅' : '失败 ❌'}`);
  
  // 测试5: 获取特定提供商的Base URL
  console.log('\n测试5: 获取特定提供商的Base URL');
  const openaiBaseUrl = configManager.getBaseUrl('openai');
  console.log(`OpenAI Base URL: ${openaiBaseUrl}`);
  console.log(`测试结果: ${openaiBaseUrl === 'https://test-api.openai.com/v1' ? '通过 ✅' : '失败 ❌'}`);
  
  // 测试6: 切换提供商
  console.log('\n测试6: 切换提供商');
  configManager.saveConfig({
    provider: 'together',
    apiKey: 'together-api-key',
    baseUrl: 'https://api.together.xyz'
  });
  
  const newConfig = configManager.getConfig();
  console.log('新配置:', newConfig);
  console.log(`测试结果: ${
    newConfig.provider === 'together' && 
    newConfig.apiKey === 'together-api-key' && 
    newConfig.baseUrl === 'https://api.together.xyz' 
      ? '通过 ✅' 
      : '失败 ❌'
  }`);
  
  // 测试7: 清除配置
  console.log('\n测试7: 清除配置');
  configManager.clearConfig();
  
  const clearedConfig = configManager.getConfig();
  console.log('清除后的配置:', clearedConfig);
  console.log(`测试结果: ${
    !cookies.get('ai_api_key_together') && 
    !cookies.get('ai_base_url_together') 
      ? '通过 ✅' 
      : '失败 ❌'
  }`);
  
  console.log('\n=== 配置管理测试完成 ===');
}

// 运行测试
runTests();
