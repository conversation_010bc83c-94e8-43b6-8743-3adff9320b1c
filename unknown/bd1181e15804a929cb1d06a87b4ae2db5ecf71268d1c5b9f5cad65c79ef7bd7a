# Project Overview

改项目的目标是对用户输入的任务进行自动化拆解，自动化运行，并实时预览过程结果的产品，当前处于开发阶段，以实际项目的代码为准。

# Personality
- 专业且技术精确
- 注重最佳实践和干净代码
- 为代码更改提供清晰的解释
- 与现有代码库保持一致的代码风格
- 提供简洁、有用且友好的交互体验

# Techstack

- 框架: Next.js 15.1.4
- 运行时: Node.js (>=18.18.0)
- 包管理器: npm
- UI: React 19 with TypeScript
- 样式: Tailwind CSS
- 代码沙箱: Sandpack、自定义组件
- 数据库: Prisma ORM
- LLM: openai兼容接口配置、xAI、Anthropic、Google、阿里云、腾讯云、火山引擎
- 代码质量: ESLint, Prettier, TypeScript
- 分析: Helicone, Plausible

# .env file

- 遵循.example.env文件中的必需环境变量
- 主要需要配置API密钥
- 不要提交.env文件（已被gitignore）

# Error Fixing Process

1. 通过错误消息和日志识别根本原因
2. 检查相关组件和依赖项
3. 验证类型安全和TypeScript合规性
4. 在提交前在本地测试更改
5. 遵循现有的错误处理模式

# Our Codebase

- 主应用程序代码在/app目录中
- 组件遵循模块化结构，位于/components目录
- 钩子函数位于/hooks目录
- 工具函数位于/lib目录
- 数据库模型和迁移位于/prisma目录
- 静态资源位于/public目录
- 测试位于/tests目录

# Current File Structure

- /app - 主应用程序代码（Next.js app router）
- /components - React组件
- /hooks - React钩子函数
- /lib - 工具函数和配置
- /prisma - 数据库模型和迁移
- /public - 静态资源
- /tests - 测试文件

# GitHub Upload Process

1. 遵循常规的提交消息格式
2. 在提交前运行linting和类型检查（`npm run lint`）
3. 为新功能创建特性分支
4. 提交PR时提供清晰的描述
5. 确保CI/CD检查通过

# Important

- 保持依赖项更新
- 遵循TypeScript严格模式
- 维护向后兼容性
- 记录API更改
- 测试跨浏览器兼容性
- 确保代码沙箱安全性

# Comments

- 使用JSDoc进行函数文档
- 保持注释清晰简洁
- 记录复杂逻辑和业务规则
- 更改代码时更新注释
- 删除冗余注释
- 始终编写与所描述代码相关的注释
- 确保注释解释"为什么"而不仅仅是"是什么"

# Code Review

- 检查类型安全
- 验证错误处理
- 确保代码遵循项目模式
- 查找性能影响
- 验证可访问性标准
- 确保代码与现有风格一致

# Code Writing

- 遵循TypeScript最佳实践
- 使用React函数组件
- 实现适当的错误边界
- 编写可测试的代码
- 遵循DRY原则（不要重复自己）
- 使用Tailwind CSS进行样式设计
- 使用Sandpack API正确集成代码沙箱

# Code Refactoring

- 维护向后兼容性
- 更新测试以配合更改
- 记录重大更改
- 遵循项目的类型系统
- 保持组件模块化和可重用
- 确保重构不会破坏现有功能

# Development Process

- 在实施解决方案之前编写3段推理段落
- 在跳到结论之前彻底分析问题空间
- 考虑所有边缘情况和潜在影响
- 以高级开发人员的思维方式处理任务
- 继续工作直到解决方案完成并验证
- 在工作时记住并考虑完整的提交/更改历史

# Code Quality Guidelines

- 代码行数越少越好，但不以牺牲可读性为代价
- 保留现有注释和文档
- 添加有意义的注释解释复杂逻辑或业务规则
- 遵循"干净代码，清晰意图"的原则
- 平衡简洁性和可维护性
- 三思而后行 - 避免过早优化
- 不要仅仅为了注释而添加注释 - 确保它们增加价值

# Problem Solving Approach

1. 在进行更改之前充分理解上下文
2. 记录您的推理和假设
3. 考虑替代方法及其权衡
4. 根据现有模式验证您的解决方案
5. 在考虑工作完成之前彻底测试
6. 审查对相关组件的影响

# UI Guidelines

- 使用一致的颜色和排版
- 确保UI响应式且可访问
- 为用户操作提供清晰的反馈
- 使用有意义的图标和标签
- 保持UI干净有序
- 使用一致的间距和对齐
- 使用一致的组件和变量命名约定
- 使用一致的文件和文件夹结构

# Style Guide

- 使用一致的组件和变量命名约定
- 使用一致的文件和文件夹结构
- 使用Tailwind CSS进行样式设计
- 遵循Next.js和React最佳实践
- 使用ESLint和Prettier保持代码风格一致
- 使用TypeScript类型确保类型安全
- 遵循现有代码库的风格