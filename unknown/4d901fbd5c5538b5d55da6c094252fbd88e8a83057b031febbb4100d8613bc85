/**
 * promptTemplates.ts
 * 文件生成格式相关的提示词模板配置，可扩展多格式/多语言。
 */

export type FileFormat = 'html' | 'markdown';

export interface FilePromptTemplate {
  format: FileFormat;
  template: string;
}

export interface PromptTemplatesConfig {
  fileInstructions: Record<FileFormat, string>;
  multiFileNote: string;
}

export const promptTemplates: PromptTemplatesConfig = {
  fileInstructions: {
    html: `**设计指导：**
    * 整体风格：可考虑贴合当前任务的最佳网站风格参考，比如现代风格、扁平化风格、杂志风格等
    * 排版：精心选择字体组合，创建清晰的视觉层次结构
    * 配色方案：选择和谐且具视觉冲击力的配色方案
    * 布局：使用基于网格的布局系统，充分利用留白
    * 数据可视化：设计直观的数据可视化元素展示关键概念
    
    **技术规范：**
    * 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
    * 实现响应式设计，在所有设备上完美展示
    * 代码结构清晰、语义化，包含适当注释
    * 可选实现深色/浅色模式切换功能
    
    **资源链接：**
    * Font Awesome: https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css
    * Tailwind CSS: https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css
    * 字体: https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap
    * Mermaid: https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js
    
    **重要提示：**
    * 请提供一个完整、可运行的HTML文件，包含所有必要的CSS和JavaScript
    * 确保代码符合W3C标准
    * 不要在HTML代码块内嵌套其他代码块标记
    * 不要在HTML代码外使用任务标记或其他非相关内容
    * 代码块前后不要添加任何无关内容或解释`,
    
      markdown: `如果需要生成Markdown代码，严格使用以下格式包裹代码，以便我可以正确提取：
    
    \`\`\`markdown{filename=文件名.md}
    你的Markdown代码
    \`\`\`
    
    **重要提示：**
    * 确保Markdown代码完整、格式正确
    * 不要在Markdown代码块内嵌套其他代码块标记
    * 不要在Markdown代码外使用任务标记或其他非相关内容
    * 代码块前后不要添加任何无关内容或解释`
    },
  multiFileNote: `如果需要生成多个文件，请为每个文件使用上述格式，并确保每个文件都有唯一的文件名。`,
};
