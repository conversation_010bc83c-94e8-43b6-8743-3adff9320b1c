<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="1200" viewBox="0 0 1200 1200" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect x="0" y="0" width="1200" height="1200" fill="#f5f7fa"/>
  
  <!-- 页面容器 -->
  <rect x="50" y="50" width="1100" height="1100" rx="8" fill="white" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 顶部导航栏 -->
  <rect x="50" y="50" width="1100" height="60" rx="8 8 0 0" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <text x="100" y="85" font-family="Arial" font-size="20" font-weight="bold" fill="#333"></text>
  
  <!-- 左侧面板 -->
  <rect x="50" y="110" width="400" height="1040" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 右侧内容区域 -->
  <rect x="450" y="110" width="700" height="1040" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 左侧面板内容 -->
  <!-- 对话历史区域 -->
  <rect x="50" y="110" width="400" height="300" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 对话历史标题 -->
  <rect x="50" y="110" width="400" height="40" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <text x="80" y="135" font-family="Arial" font-size="16" font-weight="bold" fill="#333">对话历史</text>
  
  <!-- 用户消息 1 -->
  <rect x="80" y="160" width="300" height="40" rx="18" fill="#e1f5fe" stroke="#b3e5fc" stroke-width="1"/>
  <text x="100" y="185" font-family="Arial" font-size="14" fill="#333">我需要一个电商网站的多个页面</text>
  <text x="390" y="155" font-family="Arial" font-size="12" fill="#666" text-anchor="end">你</text>
  
  <!-- AI回复 1 -->
  <rect x="120" y="210" width="300" height="40" rx="18" fill="#f5f5f5" stroke="#e0e0e0" stroke-width="1"/>
  <text x="140" y="235" font-family="Arial" font-size="14" fill="#333">请提供更多关于网站的信息</text>
  <text x="110" y="205" font-family="Arial" font-size="12" fill="#666">AI</text>
  
  <!-- 用户消息 2 -->
  <rect x="80" y="260" width="300" height="60" rx="18" fill="#e1f5fe" stroke="#b3e5fc" stroke-width="1"/>
  <text x="100" y="285" font-family="Arial" font-size="14" fill="#333">需要首页、产品列表页、产品详情页</text>
  <text x="100" y="305" font-family="Arial" font-size="14" fill="#333">和联系我们页面</text>
  <text x="390" y="255" font-family="Arial" font-size="12" fill="#666" text-anchor="end">你</text>
  
  <!-- 清除对话按钮 -->
  <rect x="320" y="120" width="120" height="25" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="380" y="137" font-family="Arial" font-size="12" fill="#666" text-anchor="middle">清除对话</text>
  
  <!-- 内容类型和选项区域 -->
  <rect x="50" y="410" width="400" height="340" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 内容类型选择 -->
  <text x="80" y="440" font-family="Arial" font-size="16" font-weight="bold" fill="#333">内容类型</text>
  
  <!-- HTML按钮 -->
  <rect x="80" y="455" width="160" height="40" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="160" y="480" font-family="Arial" font-size="14" fill="white" text-anchor="middle">HTML</text>
  
  <!-- Markdown按钮 -->
  <rect x="260" y="455" width="160" height="40" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="340" y="480" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">Markdown</text>
  
  <!-- 高级选项 -->
  <text x="80" y="525" font-family="Arial" font-size="16" font-weight="bold" fill="#333">高级选项</text>
  
  <!-- 风格选择 -->
  <text x="80" y="555" font-family="Arial" font-size="14" fill="#555">风格</text>
  <rect x="80" y="565" width="340" height="40" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="100" y="590" font-family="Arial" font-size="14" fill="#333">简约现代</text>
  <path d="M400 585 L410 575 L420 585" fill="none" stroke="#666" stroke-width="2"/>
  
  <!-- 复杂度选择 -->
  <text x="80" y="625" font-family="Arial" font-size="14" fill="#555">复杂度</text>
  <rect x="80" y="635" width="340" height="40" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="100" y="660" font-family="Arial" font-size="14" fill="#333">中等</text>
  <path d="M400 655 L410 645 L420 655" fill="none" stroke="#666" stroke-width="2"/>
  
  <!-- 文件数量选择 -->
  <text x="80" y="695" font-family="Arial" font-size="14" fill="#555">生成文件数量</text>
  <rect x="80" y="705" width="340" height="40" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="100" y="730" font-family="Arial" font-size="14" fill="#333">4个文件</text>
  <path d="M400 725 L410 715 L420 725" fill="none" stroke="#666" stroke-width="2"/>
  
  <!-- 生成按钮 -->
  <rect x="80" y="765" width="340" height="50" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="250" y="795" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">生成多个页面</text>
  
  <!-- 新消息输入区域 -->
  <rect x="50" y="1100" width="400" height="50" rx="0 0 0 8" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <rect x="70" y="1105" width="280" height="40" rx="20" fill="white" stroke="#d0d0d0" stroke-width="1"/>
  <text x="90" y="1130" font-family="Arial" font-size="14" fill="#666" opacity="0.7">输入新的提示或修改建议...</text>
  
  <!-- 发送按钮 -->
  <rect x="360" y="1105" width="70" height="40" rx="20" fill="#4a6cf7" stroke="none"/>
  <text x="395" y="1130" font-family="Arial" font-size="14" fill="white" text-anchor="middle">发送</text>
  
  <!-- 右侧内容区域 - 文件列表 -->
  <rect x="450" y="110" width="700" height="50" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <text x="480" y="140" font-family="Arial" font-size="16" font-weight="bold" fill="#333">生成的文件 (4)</text>
  
  <!-- 文件选择器 -->
  <rect x="650" y="120" width="480" height="30" rx="4" fill="#ffffff" stroke="#d0d0d0" stroke-width="1"/>
  
  <!-- 文件标签 -->
  <rect x="660" y="120" width="100" height="30" rx="4 0 0 4" fill="#4a6cf7" stroke="none"/>
  <text x="710" y="140" font-family="Arial" font-size="14" fill="white" text-anchor="middle">index.html</text>
  
  <rect x="760" y="120" width="120" height="30" rx="0" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="820" y="140" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">products.html</text>
  
  <rect x="880" y="120" width="120" height="30" rx="0" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="940" y="140" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">product.html</text>
  
  <rect x="1000" y="120" width="120" height="30" rx="0 4 4 0" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="1060" y="140" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">contact.html</text>
  
  <!-- 第一个文件内容查看器 -->
  <rect x="450" y="160" width="700" height="400" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 内容查看器工具栏 -->
  <rect x="450" y="160" width="700" height="40" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <text x="480" y="185" font-family="Arial" font-size="14" font-weight="bold" fill="#333">index.html</text>
  
  <!-- 视图模式切换 -->
  <rect x="900" y="165" width="70" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="935" y="185" font-family="Arial" font-size="14" fill="white" text-anchor="middle">代码</text>
  
  <rect x="980" y="165" width="70" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="1015" y="185" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">预览</text>
  
  <rect x="1060" y="165" width="70" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="1095" y="185" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">分屏</text>
  
  <!-- 分割线 -->
  <line x1="800" y1="200" x2="800" y2="560" stroke="#e0e0e0" stroke-width="1" stroke-dasharray="5,5"/>
  
  <!-- 左侧代码区域 -->
  <rect x="460" y="210" width="330" height="340" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 代码行号 -->
  <rect x="460" y="210" width="30" height="340" fill="#f0f0f0" stroke="none"/>
  <text x="475" y="230" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">1</text>
  <text x="475" y="250" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">2</text>
  <text x="475" y="270" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">3</text>
  <text x="475" y="290" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">4</text>
  <text x="475" y="310" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">5</text>
  
  <!-- 代码内容 -->
  <text x="500" y="230" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;!DOCTYPE html&gt;</tspan></text>
  <text x="500" y="250" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;html&gt;</tspan></text>
  <text x="500" y="270" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;head&gt;</tspan></text>
  <text x="500" y="290" font-family="Consolas, monospace" font-size="12" fill="#333">  <tspan fill="#0000ff">&lt;title&gt;</tspan>电商网站首页<tspan fill="#0000ff">&lt;/title&gt;</tspan></text>
  <text x="500" y="310" font-family="Consolas, monospace" font-size="12" fill="#333">  <tspan fill="#0000ff">&lt;style&gt;</tspan></text>
  
  <!-- 右侧预览区域 -->
  <rect x="810" y="210" width="330" height="340" rx="4" fill="white" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 预览内容 -->
  <text x="830" y="240" font-family="Arial" font-size="20" font-weight="bold" fill="#333">电商网站首页</text>
  <rect x="830" y="260" width="290" height="1" fill="#e0e0e0"/>
  
  <rect x="830" y="280" width="290" height="80" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <text x="850" y="310" font-family="Arial" font-size="16" font-weight="bold" fill="#333">热门产品</text>
  <text x="850" y="340" font-family="Arial" font-size="14" fill="#555">查看我们的精选产品系列</text>
  
  <!-- 第二个文件内容查看器 -->
  <rect x="450" y="570" width="700" height="400" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 内容查看器工具栏 -->
  <rect x="450" y="570" width="700" height="40" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <text x="480" y="595" font-family="Arial" font-size="14" font-weight="bold" fill="#333">products.html</text>
  
  <!-- 视图模式切换 -->
  <rect x="900" y="575" width="70" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="935" y="595" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">代码</text>
  
  <rect x="980" y="575" width="70" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="1015" y="595" font-family="Arial" font-size="14" fill="white" text-anchor="middle">预览</text>
  
  <rect x="1060" y="575" width="70" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="1095" y="595" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">分屏</text>
  
  <!-- 预览内容 -->
  <rect x="460" y="620" width="680" height="340" rx="4" fill="white" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 预览内容 -->
  <text x="480" y="650" font-family="Arial" font-size="20" font-weight="bold" fill="#333">产品列表</text>
  <rect x="480" y="670" width="640" height="1" fill="#e0e0e0"/>
  
  <!-- 产品卡片 -->
  <rect x="480" y="690" width="200" height="220" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <rect x="500" y="710" width="160" height="100" rx="4" fill="#e0e0e0" stroke="none"/>
  <text x="580" y="760" font-family="Arial" font-size="12" fill="#666" text-anchor="middle">[产品图片]</text>
  <text x="500" y="830" font-family="Arial" font-size="16" font-weight="bold" fill="#333">产品 1</text>
  <text x="500" y="855" font-family="Arial" font-size="14" fill="#555">¥199.00</text>
  <rect x="500" y="870" width="80" height="25" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="540" y="887" font-family="Arial" font-size="12" fill="white" text-anchor="middle">查看详情</text>
  
  <rect x="700" y="690" width="200" height="220" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <rect x="720" y="710" width="160" height="100" rx="4" fill="#e0e0e0" stroke="none"/>
  <text x="800" y="760" font-family="Arial" font-size="12" fill="#666" text-anchor="middle">[产品图片]</text>
  <text x="720" y="830" font-family="Arial" font-size="16" font-weight="bold" fill="#333">产品 2</text>
  <text x="720" y="855" font-family="Arial" font-size="14" fill="#555">¥299.00</text>
  <rect x="720" y="870" width="80" height="25" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="760" y="887" font-family="Arial" font-size="12" fill="white" text-anchor="middle">查看详情</text>
  
  <!-- 底部控制区域 -->
  <rect x="450" y="980" width="700" height="60" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 分页控制 -->
  <rect x="480" y="995" width="40" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="500" y="1015" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">1</text>
  
  <rect x="530" y="995" width="40" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="550" y="1015" font-family="Arial" font-size="14" fill="white" text-anchor="middle">2</text>
  
  <rect x="580" y="995" width="40" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="600" y="1015" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">3</text>
  
  <rect x="630" y="995" width="40" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="650" y="1015" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">4</text>
  
  <!-- 下载所有文件按钮 -->
  <rect x="1000" y="995" width="130" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="1065" y="1015" font-family="Arial" font-size="14" fill="white" text-anchor="middle">下载所有文件</text>
  
  <!-- 底部状态栏 -->
  <rect x="450" y="1050" width="700" height="40" rx="0 0 8 0" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 状态信息 -->
  <text x="480" y="1075" font-family="Arial" font-size="14" fill="#666">已生成4个文件 • 上次生成: 2023-06-15 14:30</text>
  
  <!-- 保存按钮 -->
  <rect x="1000" y="1055" width="120" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="1060" y="1075" font-family="Arial" font-size="14" fill="white" text-anchor="middle">保存所有内容</text>
</svg>
