import { PrismaClient } from "@prisma/client";
import { cache } from "react"; 

// 简单的Prisma客户端初始化，不使用适配器
export const getPrisma = cache(() => {
  try {
    console.log('🔍 初始化Prisma客户端...');
    // 直接使用Prisma Accelerate URL
    const prismaClient = new PrismaClient();
    console.log('🔍 Prisma客户端创建成功');
    return prismaClient;
  } catch (error) {
    console.error('❌ Prisma初始化错误:', error);
    throw error;
  }
});
