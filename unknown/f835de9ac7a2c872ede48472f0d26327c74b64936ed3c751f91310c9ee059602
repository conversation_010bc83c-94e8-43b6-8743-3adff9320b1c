# PR#6 性能与兼容性优化——详细设计

## 一、问题空间与目标梳理

1. **现状分析**  
   现有 `CodeDisplay` 组件采用全量渲染（`react-syntax-highlighter` 或 `<pre>`），大文件时会导致 DOM 节点爆炸、渲染卡顿，移动端和低性能设备尤为明显。PR#5 已实现全局状态与异常降级，但未对大文件渲染做专门优化。

2. **核心目标**  
   - 支持大文件/长文本的高性能流式渲染，避免全量 DOM 挂载。
   - 兼容语法高亮与纯文本两种模式，自动检测性能瓶颈并降级。
   - 保证在主流桌面与移动端环境下流畅体验，异常时可回退基础渲染。

3. **边界与约束**  
   - 需与现有流式 append、buffer、降级入口等机制兼容。
   - 不能影响编辑模式、整体刷新等功能分支。
   - 需保留原有回退与异常监控机制。

---

## 二、详细设计

### 1. 虚拟滚动集成

- **依赖选择**：采用 `react-window`（已安装），轻量、易集成，适合代码/文本行级虚拟化。
- **实现方式**：
  - 将 `content` 按行分割为数组，传递给 `FixedSizeList`。
  - 每行渲染时，支持语法高亮（高亮模式下可对每行单独高亮，降级时直接 `<pre>`）。
  - 仅渲染可视区域的行，极大降低 DOM 数量。
- **降级策略**：
  - 检测文件行数/内容长度，超阈值自动切换虚拟滚动。
  - 检测设备性能（如内存、userAgent），低性能设备强制降级。
  - 用户可手动切换“高亮/基础/虚拟滚动”模式。

### 2. 性能监控与自适应

- **监控点**：
  - 渲染耗时、内存占用、滚动流畅度。
  - 关键路径增加日志与异常捕获，便于问题定位。
- **自适应降级**：
  - 若渲染超时/卡顿，自动切换为基础 `<pre>` 或分段渲染。
  - 降级时 UI 提示，支持一键恢复高亮/虚拟滚动。

### 3. Buffer 裁剪与内存保护

- **大文件流式 append 时**，buffer 超阈值自动裁剪（如仅保留最近 N 行），防止内存溢出。
- **切换 Tab/版本/编辑模式时**，及时清理 buffer，释放内存。

### 4. 兼容性与回退

- **移动端/低性能设备**：默认基础渲染，关闭动画与高亮。
- **异常时**：自动回退到 `<pre>`，保证主流程可用。

---

## 三、变更文件与影响面

- [`components/content-viewer/code-display.tsx`](components/content-viewer/code-display.tsx:1)：核心虚拟滚动与性能降级改造
- [`components/content-viewer/types.ts`](components/content-viewer/types.ts:35)：如需扩展 props（如模式切换、降级状态）
- 相关全局状态与异常监控 hooks（如有）

---

## 四、关键代码片段（伪代码）

```tsx
import { FixedSizeList as List } from 'react-window';

const lines = content.split('\n');
const isVirtual = lines.length > 500 || forceVirtual; // 阈值可配置

return isVirtual ? (
  <List
    height={height}
    itemCount={lines.length}
    itemSize={20}
    width="100%"
  >
    {({ index, style }) => (
      <div style={style}>
        {/* 可选：每行语法高亮 */}
        <span>{lines[index]}</span>
      </div>
    )}
  </List>
) : (
  // 原有高亮或<pre>渲染
);
```

---

## 五、验收标准

- 1W+行大文件流畅渲染，滚动不卡顿，内存占用可控。
- 移动端/低性能设备兼容，降级无副作用。
- 降级/回退机制健全，异常可感知、可恢复。
- 关键路径有日志与监控，便于问题定位。

---

## 六、变更摘要

- 新增依赖：react-window  
- `CodeDisplay` 支持虚拟滚动与性能降级  
- 增强大文件流式渲染兼容性与可回退性  
- 详细设计已写入本文件，并建议在 `docs/流式输出升级-全局影响与任务拆解方案.md` 的 PR#6 小节补充“详细设计已见 pr6-性能与兼容性优化方案.md”