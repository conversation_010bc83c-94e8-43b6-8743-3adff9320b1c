// 测试文件提取功能
// 使用相对路径导入模块
const { extractMultipleFilesFromMessage } = require('./lib/code-extractor.ts');

// 如果上面的导入失败，尝试使用以下导入方式
// const codeExtractor = require('./lib/code-extractor.ts');
// const { extractMultipleFilesFromMessage } = codeExtractor;

// 测试数据
const testMessage = `用\`\`\`html{filename=hello1.html} <!DOCTYPE html> <html lang="en"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>Hello World 1</title> </head> <body> <h1>Hello, World!</h1> </body> </html> \`\`\` \`\`\`html{filename=hello2.html} <!DOCTYPE html> <html lang="en"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>Hello World 2</title> </head> <body> <h1>Hello, World!</h1> </body> </html> \`\`\` \`\`\`html{filename=hello3.html} <!DOCTYPE html> <html lang="en"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>Hello World 3</title> </head> <body> <h1>Hello, World!</h1> </body> </html> \`\`\`
问题仍然存在，用这个实际数据，测试下多文件解析`;

// 运行测试
console.log('=== TESTING FILE EXTRACTION ===');
const extractedFiles = extractMultipleFilesFromMessage(testMessage);
console.log('Extracted files count:', extractedFiles.length);

// 打印提取的文件
extractedFiles.forEach((file, index) => {
  console.log(`File ${index + 1}: ${file.filename}`);
  console.log(`Content type: ${file.contentType}`);
  console.log(`Content length: ${file.content.length}`);
  console.log(`Content preview: ${file.content.substring(0, 100)}...`);
  console.log('---');
});
