#!/bin/bash

# 默认端口
PORT=4000
LOG_FILE="dev.log"
PID_FILE="dev.pid"

# 解析参数
while getopts ":p:" opt; do
  case $opt in
    p) PORT="$OPTARG" ;;
    \?) echo "无效选项: -$OPTARG" >&2; exit 1 ;;
  esac
done

# 停止已存在的进程（如果 PID 文件存在）
if [ -f "$PID_FILE" ]; then
  echo "🛑 停止已存在的进程 (PID: $(cat $PID_FILE))"
  kill -9 $(cat "$PID_FILE") 2>/dev/null
  rm "$PID_FILE"
fi

# 启动开发服务器并后台运行
echo "🚀 启动开发服务器(端口: $PORT)"
nohup npm run dev -- --port "$PORT" > "$LOG_FILE" 2>&1 &
echo $! > "$PID_FILE"

echo "✅ 服务已启动！"
echo "📄 日志文件: $LOG_FILE"
echo "🆔 进程 PID: $(cat $PID_FILE)"
