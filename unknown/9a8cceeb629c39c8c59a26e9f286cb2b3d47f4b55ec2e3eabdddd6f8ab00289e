"use client";

import Link from "next/link";

export default function TestOpenAIPage() {
  const testPages = [
    {
      title: "配置管理测试",
      description: "测试API密钥和基础URL的保存和加载功能",
      path: "/test",
      icon: "⚙️"
    },
    {
      title: "API调用测试",
      description: "测试基本的OpenAI API调用功能",
      path: "/test-api",
      icon: "🔄"
    },
    {
      title: "流式响应测试",
      description: "测试OpenAI API的流式响应功能",
      path: "/test-stream",
      icon: "📊"
    },
    {
      title: "错误处理测试",
      description: "测试各种错误情况下的处理逻辑",
      path: "/test-error",
      icon: "⚠️"
    }
  ];
  
  return (
    <>
      <h1 className="text-2xl font-bold mb-6">OpenAI API集成测试</h1>
      
      <div className="bg-blue-50 p-4 rounded-lg mb-6 border border-blue-200">
        <h2 className="text-lg font-semibold text-blue-700 mb-2">测试说明</h2>
        <p className="text-blue-600">
          本页面提供了一系列测试工具，用于验证OpenAI API集成的各项功能。请按照以下顺序进行测试：
        </p>
        <ol className="list-decimal list-inside mt-2 text-blue-600 space-y-1">
          <li>首先测试配置管理功能，确保API密钥和基础URL能够正确保存和加载</li>
          <li>然后测试基本的API调用功能，验证能够成功调用OpenAI API</li>
          <li>接着测试流式响应功能，验证能够正确处理流式数据</li>
          <li>最后测试错误处理逻辑，确保应用能够正确处理各种错误情况</li>
        </ol>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {testPages.map((page) => (
          <Link 
            key={page.path} 
            href={page.path}
            className="block bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200"
          >
            <div className="flex items-center mb-3">
              <span className="text-2xl mr-3">{page.icon}</span>
              <h2 className="text-xl font-semibold">{page.title}</h2>
            </div>
            <p className="text-gray-600">{page.description}</p>
          </Link>
        ))}
      </div>
      
      <div className="mt-8 bg-gray-50 p-6 rounded-lg border border-gray-200">
        <h2 className="text-lg font-semibold mb-4">测试结果记录</h2>
        <p className="text-gray-600 mb-4">
          完成测试后，请将测试结果记录在<code className="bg-gray-100 px-2 py-1 rounded">测试结果-OpenAI.md</code>文件中。
        </p>
        <Link 
          href="/"
          className="inline-block px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          返回首页
        </Link>
      </div>
    </>
  );
}
