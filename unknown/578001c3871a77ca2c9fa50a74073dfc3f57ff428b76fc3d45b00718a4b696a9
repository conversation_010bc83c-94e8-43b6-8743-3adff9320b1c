/**
 * 存储键常量
 */
export const CUSTOM_MODELS_KEY = "custom_models";
export const ENDPOINTS_KEY = "endpoints";

/**
 * 自定义模型配置接口
 */
export interface CustomModel {
  label: string;     // 显示名称
  value: string;     // API调用值
  endpoint?: string; // 关联端点（可选）
}

/**
 * 端点配置接口
 */
export interface Endpoint {
  name: string;      // 端点名称
  baseUrl: string;   // 基础URL
  apiKey?: string;   // API密钥（可选）
}

/**
 * 配置文件结构
 */
export interface ConfigFile {
  endpoints: Endpoint[];
  customModels: CustomModel[];
}

/**
 * 提供商模型配置
 */
export const PROVIDER_MODELS = {
  together: [
    {
      label: "Qwen 2.5 Coder 32B",
      value: "Qwen/Qwen2.5-Coder-32B-Instruct",
    },
    {
      label: "Llama 3.1 405B",
      value: "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
    },
    {
      label: "Llama 3.3 70B",
      value: "meta-llama/Llama-3.3-70B-Instruct-Turbo",
    },
    {
      label: "DeepSeek V3",
      value: "deepseek-ai/DeepSeek-V3",
    },
  ],
  openai: [
    {
      label: "GPT-4o",
      value: "gpt-4o",
    },
    {
      label: "GPT-4 Turbo",
      value: "gpt-4-turbo",
    },
    {
      label: "GPT-3.5 Turbo",
      value: "gpt-3.5-turbo",
    },
  ],
};

/**
 * 提供商列表
 */
export const PROVIDERS = [
  {
    label: "OpenAI Compatible",
    value: "together",
  },
  {
    label: "OpenAI",
    value: "openai",
  },
];

/**
 * 默认的OpenAI API端点配置
 */
export const DEFAULT_OPENAI_ENDPOINT = {
  name: "OpenAI Official",
  baseUrl: "https://api.openai.com/v1",
  models: PROVIDER_MODELS.openai,
};

// 默认使用OpenAI Compatible的模型列表
export const MODELS = PROVIDER_MODELS.together;

export const SUGGESTED_PROMPTS = [
  {
    title: "Quiz app",
    description:
      "Make me a quiz app about American history. Make sure to give the user an explanation on each question whether they got it right or wrong and keep a score going",
  },
  {
    title: "SaaS Landing page",
    description:
      "A landing page for a SaaS business that includes a clear value proposition in a prominent hero section, concise feature overviews, testimonials, pricing, and a clear call-to-action button leading to a free trial or demo.",
  },
  {
    title: "Pomodoro Timer",
    description:
      "Make a beautiful pomodoro timer where I can adjust the lengths of the focus time and the break and it will beep when done.",
  },
  {
    title: "Blog app",
    description:
      "Make me a blog app that has a few blogs there for people to read. Users can click into the blogs and read them, then go back to the homepage to see more.",
  },
  // {
  //   title: "Recipe site",
  //   description:
  //     "Make me a site that has easy to make recipes in a grid that you can click into and see the full recipe. Also make it possible for me to add my own",
  // },
  {
    title: "Flashcard app",
    description:
      "Build me a flashcard app about llamas. Have some flash cards and also have the ability for users to add their own. Show one side of a card at first and reveal the answer on button click, keeping track of correct guesses to measure progress.",
  },
  {
    title: "Timezone dashboard",
    description:
      "Make me a time zone dashboard that shows me the time zone in the top 6 most popular time zones and gives me a dropdown to add others",
  },
];
