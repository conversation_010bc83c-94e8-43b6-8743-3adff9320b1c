'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { MarkdownPreviewProps } from './types';

const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ content }) => {
  const [isClient, setIsClient] = useState(false);
  const [ReactMarkdown, setReactMarkdown] = useState<any>(null);
  const [remarkGfm, setRemarkGfm] = useState<any>(null);

  // 缓存处理后的内容
  const processedContent = useMemo(() => {
    // 处理可能的不完整Markdown
    try {
      // 检查是否有不匹配的代码块
      const codeBlockRegex = /```(\w*)\n([\s\S]*?)```/g;
      let match;
      let lastIndex = 0;
      let hasUnclosedCodeBlock = false;

      while ((match = codeBlockRegex.exec(content)) !== null) {
        lastIndex = match.index + match[0].length;
      }

      // 检查是否有未关闭的代码块
      const remainingContent = content.slice(lastIndex);
      const unclosedBlockMatch = remainingContent.match(/```(\w*)\n([\s\S]*?)$/);

      if (unclosedBlockMatch) {
        hasUnclosedCodeBlock = true;
      }

      return hasUnclosedCodeBlock ? content + '\n```' : content;
    } catch (error) {
      console.error('Error processing Markdown:', error);
      return content;
    }
  }, [content]);

  // 在客户端动态导入ReactMarkdown和remarkGfm
  useEffect(() => {
    const loadDependencies = async () => {
      try {
        const [markdownModule, gfmModule] = await Promise.all([
          import('react-markdown'),
          import('remark-gfm')
        ]);

        setReactMarkdown(() => markdownModule.default);
        setRemarkGfm(() => gfmModule.default);
        setIsClient(true);
      } catch (error) {
        console.error('Error loading Markdown dependencies:', error);
      }
    };

    loadDependencies();
  }, []);

  // 简单的Markdown预览，用于在ReactMarkdown加载前显示
  const SimpleMarkdownPreview = ({ content }: { content: string }) => {
    // 将Markdown转换为简单的HTML
    const html = content
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/\*\*(.*)\*\*/gm, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gm, '<em>$1</em>')
      .replace(/!\[(.*?)\]\((.*?)\)/gm, '<img alt="$1" src="$2" />')
      .replace(/\[(.*?)\]\((.*?)\)/gm, '<a href="$2">$1</a>')
      .replace(/^> (.*$)/gm, '<blockquote>$1</blockquote>')
      .replace(/^- (.*$)/gm, '<ul><li>$1</li></ul>')
      .replace(/^(\d+)\. (.*$)/gm, '<ol><li>$2</li></ol>')
      .replace(/^```([\s\S]*?)```$/gm, '<pre><code>$1</code></pre>')
      .replace(/`([^`]+)`/gm, '<code>$1</code>')
      .replace(/\n/gm, '<br />');

    return <div dangerouslySetInnerHTML={{ __html: html }} />;
  };

  return (
    <div className="w-full h-full relative overflow-auto bg-white">
      <div className="absolute top-0 left-0 w-full h-full p-6 overflow-auto bg-white">
        {isClient && ReactMarkdown ? (
          <article className="prose prose-slate max-w-none">
            <ReactMarkdown
              remarkPlugins={remarkGfm ? [remarkGfm] : []}
            >
              {processedContent}
            </ReactMarkdown>
          </article>
        ) : (
          <SimpleMarkdownPreview content={processedContent} />
        )}
      </div>
    </div>
  );
};

export default MarkdownPreview;
