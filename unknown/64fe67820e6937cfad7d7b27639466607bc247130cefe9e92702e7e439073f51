// Vitest 全局配置文件
// 适用于React/TypeScript/前端hooks测试，确保describe/it等全局API可用
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    environment: 'jsdom', // 适合大多数前端/React测试
    include: [
      'tests/**/*.test.{ts,tsx,js,jsx}',
      'tests/**/*.unit.test.{ts,tsx,js,jsx}'
    ],
    globals: true, // 启用describe/it等全局API
    coverage: {
      reporter: ['text', 'json', 'html'],
    },
  },
});