# PR#5 全局状态管理与异常处理适配——详细设计

## 一、目标与范围

- **目标**：实现全局 store/Context 对流式与非流式状态的无缝同步，确保异常场景下可感知、可恢复、可降级，保障平台稳定性。
- **范围**：
  - 全局状态（如任务、文件、AI会话、UI等）在流式/非流式切换下的同步与一致性
  - 异常检测、提示与自动恢复机制
  - 回退与降级策略的集成

---

## 二、核心设计要点

### 1. 全局状态同步机制

- **状态分层**：采用分层 Context（如`AppContext`、`TaskContext`、`FileContext`等），每层负责自身领域的状态管理，顶层统一调度。
- **流式/非流式模式标识**：全局 store 增加 `isStreamingMode` 标志，所有核心状态变更均需感知该模式。
- **事件驱动同步**：流式事件（如SSE/ReadableStream）通过事件分发器（EventEmitter/Redux action）驱动状态变更，确保 UI 与数据一致。
- **状态快照与回滚**：关键状态（如任务树、文件内容、AI会话）支持快照保存，异常时可一键回滚。

### 2. 异常检测与处理

- **异常类型**：
  - 流中断（如SSE断开、网络异常）
  - 内容拼接失败（如hash校验失败）
  - 状态同步延迟或错乱
  - 性能瓶颈（如大文件渲染卡顿）
- **异常感知**：
  - 全局异常捕获（如ErrorBoundary、全局try-catch、事件监听）
  - 关键流程埋点与日志（如流式事件、状态变更、回退操作）
- **自动恢复与提示**：
  - 支持自动重连、内容重试、状态重置
  - 异常弹窗/Toast提示，必要时引导用户手动恢复或切换模式

### 3. 回退与降级机制

- **一键切换**：全局提供“切回非流式”入口，异常时自动降级，保障主流程可用
- **状态一致性校验**：切换前后进行状态快照比对，确保无数据丢失/错乱
- **降级策略**：如性能不足时自动关闭动画/高亮，仅保留基础流式渲染

### 4. 监控与可观测性

- **日志与埋点**：关键状态变更、异常、回退操作均需记录日志，便于问题定位
- **监控面板**：开发环境下可视化全局状态、异常统计、回退历史

---

## 三、关键状态与方法示例

```typescript
// 以全局 AppContext 为例
interface AppState {
  isStreamingMode: boolean;
  globalError: string | null;
  lastSnapshot: AppSnapshot | null;
  // ...其他全局状态
}

const [state, dispatch] = useReducer(appReducer, initialState);

// 切换流式/非流式模式
function toggleStreamingMode(isStreaming: boolean) {
  dispatch({ type: 'SET_STREAMING_MODE', payload: isStreaming });
  // 保存快照
  dispatch({ type: 'SAVE_SNAPSHOT' });
}

// 异常捕获与恢复
function handleGlobalError(error: Error) {
  dispatch({ type: 'SET_GLOBAL_ERROR', payload: error.message });
  // 自动降级
  if (state.isStreamingMode) {
    toggleStreamingMode(false);
  }
}

// 回退操作
function rollbackToLastSnapshot() {
  if (state.lastSnapshot) {
    dispatch({ type: 'ROLLBACK_SNAPSHOT', payload: state.lastSnapshot });
  }
}
```

---

## 四、变更文件与影响面

- `lib/ai-store.ts`：全局状态管理核心，需支持流式/非流式切换、异常捕获与回退
- `lib/streaming/stream-handler.ts`、`lib/streaming/stream-client.ts`：流式事件分发与异常处理
- `components/content-viewer/content-viewer.tsx`、`components/conversation-panel/conversation-panel.tsx`：UI层感知全局状态与异常
- `hooks/`：如有自定义 useStore/useStreaming 等 hook，需适配新状态与异常处理
- 其他涉及全局状态的 Context/Provider

---

## 五、验收标准

- 流式与非流式状态同步无错乱，切换流畅
- 异常场景（如流中断、拼接失败、性能瓶颈）可被感知并自动恢复或降级
- 回退机制健全，切换无副作用
- 关键流程有日志与监控，便于问题定位

---

## 六、Mermaid 状态流与异常处理关系图

```mermaid
flowchart TD
    S[流式事件] --> |驱动| ST[全局状态变更]
    ST --> UI[UI同步]
    ST --> SNAP[状态快照]
    S --> ERR[异常检测]
    ERR --> |自动| REC[自动恢复/降级]
    ERR --> |手动| RB[回退操作]
    REC --> ST
    RB --> ST
    ST --> LOG[日志/监控]
```

---

## 七、变更摘要与后续计划

- 变更文件：`lib/ai-store.ts`、`lib/streaming/stream-handler.ts`、`components/content-viewer/content-viewer.tsx` 等
- 主要改动：全局状态结构调整、异常处理与回退机制集成、UI层异常提示与降级入口
- 产物清单：详细设计文档、状态流与异常处理关系图、变更点列表
- 下一步：按设计逐步实现，单元测试与集成测试覆盖异常与回退场景