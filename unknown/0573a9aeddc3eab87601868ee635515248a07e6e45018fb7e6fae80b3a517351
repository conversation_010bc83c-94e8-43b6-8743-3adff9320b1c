"use server";


"use server";

import { getPrisma } from "@/lib/prisma";
import {
  getMainCodingPrompt,
  screenshotToCodePrompt,
  softwareArchitectPrompt,
} from "@/lib/prompts";
import { notFound } from "next/navigation";
import { ProviderFactory, ProviderType } from "@/lib/provider-factory";
import { cookies } from "next/headers";
import { AIConfigManager, COOKIE_KEYS } from "@/lib/ai-config";
import { Cookies } from "next-client-cookies";

// 检测是否在Edge Runtime环境中运行（使用类型安全的方式）
const isEdge = (): boolean => {
  return (typeof process !== 'undefined' && 
    process.env.NEXT_RUNTIME === 'edge') || 
    (typeof window === 'undefined' && 
    typeof globalThis !== 'undefined' && 
    typeof (globalThis as any).EdgeRuntime !== 'undefined');
};

export async function createChat(
  prompt: string,
  model: string,
  quality: "high" | "low",
  screenshotUrl: string | undefined,
  clientProvider?: ProviderType,
) {
  try {
    console.log('🔍 createChat 开始执行', { 
      prompt, 
      model, 
      quality, 
      hasScreenshot: !!screenshotUrl, 
      clientProvider,
      isEdgeRuntime: isEdge(),
      runtime: process.env.NEXT_RUNTIME
    });
    
    const prisma = getPrisma();
    console.log('🔍 获取 Prisma 实例成功');
    
    let chat;
    try {
      // 在Edge环境中，添加额外日志
      if (isEdge()) {
        console.log('🔍 在Edge Runtime环境中执行数据库操作');
      }
      
      chat = await prisma.chat.create({
        data: {
          model,
          quality,
          prompt,
          title: "",
          shadcn: true,
        },
      });
      console.log('🔍 创建聊天记录成功', { chatId: chat.id });
    } catch (dbError) {
      const error = dbError as Error;
      console.error('❌ 数据库错误 (创建聊天):', {
        message: error.message || '未知错误',
        name: error.name || '未知错误类型',
        stack: error.stack?.substring(0, 200) || '无堆栈信息' // 只显示堆栈的前200个字符
      });
      throw new Error(`数据库操作失败: ${error.message || '未知错误'}`);
    }

    // 获取配置
    console.log('🔍 开始获取配置');
    let provider: ProviderType = 'openai';
    let apiKey: string | undefined;
    let baseUrl: string | undefined;
    
    try {
      // 如果客户端传入了provider，优先使用客户端传入的provider
      if (clientProvider) {
        provider = clientProvider;
        console.log('🔍 使用客户端传入的provider:', { provider, type: typeof provider });
      } else {
        // 否则从cookies中获取provider
        // 直接从cookies()中获取所需的值
        const cookieStore = await cookies();
        console.log('🔍 获取到cookieStore:', { type: typeof cookieStore });
        
        // 获取provider
        const providerValue = cookieStore.get(COOKIE_KEYS.PROVIDER);
        // 从RequestCookie对象中提取value属性
        if (providerValue) {
          provider = providerValue.value as ProviderType;
        }
        console.log('🔍 从cookies获取到provider:', { provider, type: typeof provider });
      }
      
      // 直接从cookies()中获取所需的值
      const cookieStore = await cookies();
      console.log('🔍 获取到cookieStore:', { type: typeof cookieStore });
      
      // 获取model
      const modelValue = cookieStore.get(COOKIE_KEYS.MODEL);
      const model = modelValue ? modelValue.value : 'gpt-3.5-turbo';
      console.log('🔍 获取到model:', { model, type: typeof model });
      
      // 获取apiKey
      const apiKeyValue = cookieStore.get(`${COOKIE_KEYS.API_KEY_PREFIX}${provider}`);
      apiKey = apiKeyValue ? apiKeyValue.value : undefined;
      console.log('🔍 获取到apiKey:', { provider, hasApiKey: !!apiKey });
      
      // 检查OpenAI提供商是否有API密钥
      if (provider === 'openai' && !apiKey) {
        console.error(`❌ ${provider}的API密钥未定义，这是必需的`);
        throw new Error(`${provider}的API密钥未定义，请在设置中配置API密钥`);
      }
      
      // 获取baseUrl
      const baseUrlValue = cookieStore.get(`${COOKIE_KEYS.BASE_URL_PREFIX}${provider}`);
      baseUrl = baseUrlValue ? baseUrlValue.value : undefined;
      console.log('🔍 获取到baseUrl:', { hasBaseUrl: !!baseUrl });
      
      console.log('🔍 获取配置成功', { 
        provider, 
        providerType: typeof provider,
        hasApiKey: !!apiKey, 
        hasBaseUrl: !!baseUrl 
      });
    } catch (configError) {
      console.error('❌ 配置错误:', configError);
      const error = configError as Error;
      throw new Error(`获取配置失败: ${error?.message || '未知错误'}`);
    }

    // 获取Provider实例
    let providerInstance: any;
    try {
      console.log('创建提供商实例:', { 
        provider, 
        providerType: typeof provider,
        hasApiKey: !!apiKey, 
        hasBaseUrl: !!baseUrl 
      });
      
      providerInstance = await ProviderFactory.getProvider({
        type: provider,
        apiKey,
        baseUrl
      });
      console.log('🔍 获取Provider实例成功', { 
        providerType: provider,
        providerTypeOf: typeof provider
      });
    } catch (providerError) {
      console.error('❌ Provider错误:', providerError);
      const error = providerError as Error;
      throw new Error(`获取AI提供商实例失败: ${error?.message || '未知错误'}`);
    }

  // 获取聊天标题
  async function fetchTitle() {
    // 使用较小的模型获取标题
    try {
      console.log('🔍 开始获取聊天标题');
      const titleModel = provider === 'openai' ? 'gpt-3.5-turbo' : 'meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo';
      console.log('🔍 使用模型获取标题', { titleModel });
    
      let titleResponse;
      try {
        if (provider === 'openai') {
          const openaiInstance = providerInstance as any;
          titleResponse = await openaiInstance.chat.completions.create({
            model: titleModel,
            messages: [
              {
                role: "system",
                content:
                  "You are a chatbot helping the user create a simple app or script, and your current job is to create a succinct title, maximum 3-5 words, for the chat given their initial prompt. Please return only the title.",
              },
              {
                role: "user",
                content: prompt,
              },
            ],
            stream: false,
          });
          console.log('🔍 OpenAI 标题响应成功');
        } else {
          const togetherInstance = providerInstance as any;
          titleResponse = await togetherInstance.chat.completions.create({
            model: titleModel,
            messages: [
              {
                role: "system",
                content:
                  "You are a chatbot helping the user create a simple app or script, and your current job is to create a succinct title, maximum 3-5 words, for the chat given their initial prompt. Please return only the title.",
              },
              {
                role: "user",
                content: prompt,
              },
            ],
            stream: false,
          });
          console.log('🔍 OpenAI Compatible 标题响应成功');
        }
      } catch (apiError) {
        console.error('❌ 获取标题API错误:', apiError);
        return prompt.substring(0, 50) + '...'; // 使用提示的前50个字符作为标题
      }
    
    // 解析响应以获取标题
    let title = '';
    try {
      if (titleResponse instanceof ReadableStream) {
        const reader = titleResponse.getReader();
        const decoder = new TextDecoder();
        let done = false;
        
        while (!done) {
          const { value, done: doneReading } = await reader.read();
          done = doneReading;
          if (value) {
            title += decoder.decode(value);
          }
        }
      } else if (provider === 'openai') {
        title = titleResponse.choices[0]?.message?.content || '';
      } else {
        title = titleResponse.choices[0]?.message?.content || '';
      }
      
      console.log('🔍 解析标题成功', { title: title || '(空标题)' });
      return title || prompt;
    } catch (parseError) {
      console.error('❌ 解析标题错误:', parseError);
      return prompt.substring(0, 50) + '...';
    }
    } catch (error) {
      console.error('❌ 获取标题总体错误:', error);
      return prompt.substring(0, 50) + '...';
    }
  }

  // 获取最相似的示例
  async function fetchTopExample() {
    // 使用较小的模型获取示例
    try {
      console.log('🔍 开始获取最相似示例');
      const exampleModel = provider === 'openai' ? 'gpt-3.5-turbo' : 'meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo';
      console.log('🔍 使用模型获取示例', { exampleModel });
      
      let exampleResponse;
      try {
        if (provider === 'openai') {
          const openaiInstance = providerInstance as any;
          exampleResponse = await openaiInstance.chat.completions.create({
            model: exampleModel,
            messages: [
              {
                role: "system",
                content: `You are a helpful bot. Given a request for building an app, you match it to the most similar example provided. If the request is NOT similar to any of the provided examples, return "none". Here is the list of examples, ONLY reply with one of them OR "none":

              - landing page
              - blog app
              - quiz app
              - pomodoro timer
              `,
              },
              {
                role: "user",
                content: prompt,
              },
            ],
            stream: false,
          });
          console.log('🔍 OpenAI 示例响应成功');
        } else {
          const togetherInstance = providerInstance as any;
          exampleResponse = await togetherInstance.chat.completions.create({
            model: exampleModel,
            messages: [
              {
                role: "system",
                content: `You are a helpful bot. Given a request for building an app, you match it to the most similar example provided. If the request is NOT similar to any of the provided examples, return "none". Here is the list of examples, ONLY reply with one of them OR "none":

              - landing page
              - blog app
              - quiz app
              - pomodoro timer
              `,
              },
              {
                role: "user",
                content: prompt,
              },
            ],
            stream: false,
          });
          console.log('🔍 OpenAI Compatible 示例响应成功');
        }
      } catch (apiError) {
        console.error('❌ 获取示例API错误:', apiError);
        return "none";
      }
    
    // 解析响应以获取示例
    let mostSimilarExample = '';
    try {
      if (exampleResponse instanceof ReadableStream) {
        const reader = exampleResponse.getReader();
        const decoder = new TextDecoder();
        let done = false;
        
        while (!done) {
          const { value, done: doneReading } = await reader.read();
          done = doneReading;
          if (value) {
            mostSimilarExample += decoder.decode(value);
          }
        }
      } else if (provider === 'openai') {
        mostSimilarExample = exampleResponse.choices[0]?.message?.content || '';
      } else {
        mostSimilarExample = exampleResponse.choices[0]?.message?.content || '';
      }
      
      console.log('🔍 解析示例成功', { mostSimilarExample: mostSimilarExample || 'none' });
      return mostSimilarExample || "none";
    } catch (parseError) {
      console.error('❌ 解析示例错误:', parseError);
      return "none";
    }
    } catch (error) {
      console.error('❌ 获取示例总体错误:', error);
      return "none";
    }
  }

  let title, mostSimilarExample, fullScreenshotDescription;
  try {
    console.log('🔍 开始并行获取标题和示例');
    [title, mostSimilarExample] = await Promise.all([
      fetchTitle(),
      fetchTopExample(),
    ]);
    console.log('🔍 并行获取标题和示例成功');
  } catch (parallelError) {
    console.error('❌ 并行获取标题和示例错误:', parallelError);
    title = prompt.substring(0, 50) + '...';
    mostSimilarExample = "none";
  }

  // 处理截图（如果有）
  if (screenshotUrl) {
    try {
      console.log('🔍 开始处理截图', { screenshotUrl });
      // 注意：OpenAI需要特殊处理截图
      if (provider === 'openai') {
        try {
          console.log('🔍 使用OpenAI Vision模型处理截图');
          // 使用OpenAI的vision模型
          const openaiInstance = providerInstance as any;
          const visionResponse = await openaiInstance.chat.completions.create({
            model: 'gpt-4-vision-preview',
            messages: [
              {
                role: "system",
                content: screenshotToCodePrompt,
              },
              {
                role: "user",
                content: [
                  { type: "text", text: "Here is the screenshot:" },
                  { type: "image_url", image_url: { url: screenshotUrl } },
                ],
              },
            ],
            max_tokens: 4000,
          });
          
          fullScreenshotDescription = visionResponse.choices[0]?.message?.content || '';
          console.log('🔍 截图处理成功', { descriptionLength: fullScreenshotDescription.length });
        } catch (visionError) {
          console.error('❌ Vision API错误:', visionError);
          fullScreenshotDescription = "Error processing screenshot.";
        }
      } else {
        // 对于其他提供商，可能需要不同的处理方式
        console.log('🔍 当前提供商不支持截图处理');
        fullScreenshotDescription = "Screenshot processing is not supported for this provider.";
      }
    } catch (screenshotError) {
      console.error('❌ 截图处理总体错误:', screenshotError);
      fullScreenshotDescription = "Error processing screenshot.";
    }
  }

  // 创建系统消息
  let systemMessage;
  try {
    console.log('🔍 创建系统消息', { 
      quality, 
      mostSimilarExample, 
      hasScreenshotDescription: !!fullScreenshotDescription 
    });
    
    // 根据getMainCodingPrompt函数的定义，它只接受一个参数
    systemMessage = getMainCodingPrompt(mostSimilarExample);
    
    // 如果需要，可以在这里添加其他处理逻辑
    if (fullScreenshotDescription) {
      console.log('🔍 添加截图描述到系统消息');
      // 这里可以添加截图描述到系统消息中
      // 但需要确保与getMainCodingPrompt函数的返回值兼容
    }
    
    console.log('🔍 系统消息创建成功', { messageLength: systemMessage.length });
  } catch (promptError) {
    console.error('❌ 创建系统消息错误:', promptError);
      const error = promptError as Error;
    throw new Error(`创建系统消息失败: ${error?.message || '未知错误'}`);
  }

  // 创建用户消息
  try {
    console.log('🔍 创建系统角色消息');
    await prisma.message.create({
      data: {
        chatId: chat.id,
        role: "system",
        content: systemMessage,
        position: 0,
      },
    });
    console.log('🔍 系统角色消息创建成功');
  } catch (messageError) {
    console.error('❌ 创建系统角色消息错误:', messageError);
      const error = messageError as Error;
    throw new Error(`创建系统角色消息失败: ${error?.message || '未知错误'}`);
  }

  // 创建用户消息
  try {
    console.log('🔍 创建用户消息');
    await prisma.message.create({
      data: {
        chatId: chat.id,
        role: "user",
        content: prompt,
        position: 1,
      },
    });
    console.log('🔍 用户消息创建成功');
  } catch (messageError) {
    console.error('❌ 创建用户消息错误:', messageError);
      const error = messageError as Error;
    throw new Error(`创建用户消息失败: ${error?.message || '未知错误'}`);
  }

  // 创建助手消息（初始为空，将通过流式响应填充）
  let message;
  try {
    console.log('🔍 创建助手消息');
    message = await prisma.message.create({
      data: {
        chatId: chat.id,
        role: "assistant",
        content: "",
        position: 2,
      },
    });
    console.log('🔍 助手消息创建成功', { messageId: message.id });
  } catch (messageError) {
    console.error('❌ 创建助手消息错误:', messageError);
      const error = messageError as Error;
    throw new Error(`创建助手消息失败: ${error?.message || '未知错误'}`);
  }

  // 更新聊天标题
  try {
    console.log('🔍 更新聊天标题');
    await prisma.chat.update({
      where: { id: chat.id },
      data: { title },
    });
    console.log('🔍 聊天标题更新成功');
  } catch (updateError) {
    console.error('❌ 更新聊天标题错误:', updateError);
    // 不抛出错误，因为这不是关键操作
  }

    console.log('🔍 createChat 执行完成', { chatId: chat.id, messageId: message.id });
    return { chatId: chat.id, messageId: message.id };
  } catch (error) {
    console.error('❌ createChat 总体错误:', error);
    throw error; // 重新抛出错误，让调用者处理
  }
}

export async function createMessage(
  chatId: string,
  text: string,
  role: "assistant" | "user",
) {
  const prisma = getPrisma();

  const chat = await prisma.chat.findUnique({
    where: { id: chatId },
    include: { messages: { orderBy: { position: "desc" }, take: 1 } },
  });

  if (!chat) {
    notFound();
  }

  const position = chat.messages[0]?.position + 1 || 0;

  const message = await prisma.message.create({
    data: {
      chatId,
      content: text,
      role,
      position,
    },
  });

  return message;
}
