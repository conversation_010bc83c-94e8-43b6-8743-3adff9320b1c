import { Cookies } from 'next-client-cookies';
import { ProviderType } from './provider-factory';
import { CustomModel, Endpoint } from './constants';

// 检测是否在Edge Runtime环境中运行
const isEdge = typeof process !== 'undefined' && 
  process.env.NEXT_RUNTIME === 'edge' || 
  typeof window === 'undefined' && 
  typeof globalThis !== 'undefined' && 
  typeof (globalThis as any).EdgeRuntime !== 'undefined';

// 配置接口
export interface AIConfig {
  provider: ProviderType;
  model: string;
  apiKey?: string;
  baseUrl?: string;
  customModels?: CustomModel[];
  endpoints?: Endpoint[];
}

// Cookie键名
export const COOKIE_KEYS = {
  PROVIDER: 'ai_provider',
  MODEL: 'ai_model',
  API_KEY_PREFIX: 'ai_api_key_',
  BASE_URL_PREFIX: 'ai_base_url_',
  CUSTOM_MODELS: 'ai_custom_models',
  ENDPOINTS: 'ai_endpoints',
};

// 默认配置
const DEFAULT_CONFIG: AIConfig = {
  provider: 'together',
  model: 'meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo',
};

/**
 * 尝试解析JSON字符串或处理对象
 * @param value 要解析的值
 * @param defaultValue 解析失败时的默认值
 * @returns 解析结果
 */
function tryParseJSON<T extends unknown[]>(value: any, defaultValue: T): T {
  if (!value) {
    console.log('🔍 值为空，返回默认值');
    return defaultValue;
  }

  try {
    // 如果已经是数组类型，直接返回
    if (Array.isArray(value)) {
      console.log('🔍 值已经是数组类型，直接返回');
      return value as T;
    }

    // 如果是对象类型
    if (typeof value === 'object' && value !== null) {
      // 如果有value属性，处理value属性
      if ('value' in value) {
        console.log('🔍 对象有value属性，处理value属性:', value.value);
        return tryParseJSON(value.value, defaultValue);
      }
      // 如果是普通对象，尝试将其作为数组返回
      if (Object.keys(value).length > 0) {
        console.log('🔍 值是普通对象，尝试作为数组返回');
        return [value] as unknown as T;
      }
      return defaultValue;
    }
    
    // 如果是字符串，尝试解析
    if (typeof value === 'string') {
      try {
        console.log('🔍 尝试解析字符串:', value);
        // 如果字符串是[object Object]，返回默认值
        if (value === '[object Object]') {
          console.log('🔍 字符串是[object Object]，返回默认值');
          return defaultValue;
        }
        const parsed = JSON.parse(value);
        if (Array.isArray(parsed)) {
          return parsed as T;
        }
        // 如果解析结果是对象，尝试将其作为数组返回
        if (typeof parsed === 'object' && parsed !== null) {
          return [parsed] as unknown as T;
        }
        return defaultValue;
      } catch (error) {
        console.error('❌ JSON解析失败:', error);
        return defaultValue;
      }
    }

    console.log('🔍 未知类型，返回默认值:', value);
    return defaultValue;
  } catch (error) {
    console.error('❌ 处理失败:', error);
    console.log('📝 原始值:', value);
    console.log('📝 值类型:', typeof value);
    return defaultValue;
  }
}

/**
 * AI配置管理类
 * 负责管理AI提供商和模型的配置，包括API密钥和自定义基础URL
 */
export class AIConfigManager {
  private cookies: Cookies;

  constructor(cookies: Cookies) {
    this.cookies = cookies;
  }

  /**
   * 获取当前配置
   */
  async getConfig(): Promise<AIConfig> {
    // 获取provider并确保它是字符串类型
    let providerValue = await this.getCookieValue(COOKIE_KEYS.PROVIDER);
    let provider: ProviderType;
    
    // 处理provider值
    if (!providerValue) {
      provider = DEFAULT_CONFIG.provider;
      console.log(`🔍 使用默认provider: ${provider}`);
    } else if (typeof providerValue === 'object' && providerValue !== null) {
      // 如果是对象，尝试提取value属性
      if ('value' in providerValue) {
        console.log(`🔍 Provider是对象类型，提取value属性`, providerValue);
        provider = providerValue.value as ProviderType;
      } else {
        console.log(`🔍 Provider是对象类型，尝试转换为字符串`, providerValue);
        provider = String(providerValue) as ProviderType;
      }
    } else {
      provider = providerValue as ProviderType;
    }
    
    console.log(`🔍 最终provider值: ${provider}, 类型: ${typeof provider}`);
    
    // 获取model
    let modelValue = await this.getCookieValue(COOKIE_KEYS.MODEL);
    let model: string;
    
    if (!modelValue) {
      model = DEFAULT_CONFIG.model;
      console.log(`🔍 使用默认model: ${model}`);
    } else if (typeof modelValue === 'object' && modelValue !== null) {
      // 如果是对象，尝试提取value属性
      if ('value' in modelValue) {
        console.log(`🔍 Model是对象类型，提取value属性`, modelValue);
        model = modelValue.value;
      } else {
        console.log(`🔍 Model是对象类型，尝试转换为字符串`, modelValue);
        model = String(modelValue);
      }
    } else {
      model = modelValue;
    }
    
    console.log(`🔍 最终model值: ${model}, 类型: ${typeof model}`);
    
    // 获取apiKey和baseUrl
    const apiKey = await this.getApiKey(provider);
    const baseUrl = await this.getBaseUrl(provider);
    
    // 获取自定义模型和端点配置
    const customModels = await this.getCustomModels();
    const endpoints = await this.getEndpoints();

    return {
      provider,
      model,
      apiKey,
      baseUrl,
      customModels,
      endpoints,
    };
  }

  /**
   * 保存配置
   */
  async saveConfig(config: Partial<AIConfig>): Promise<void> {
    const currentConfig = await this.getConfig();
    const newConfig = { ...currentConfig, ...config };

    // 保存提供商和模型
    if (config.provider) {
      await this.setCookieValue(COOKIE_KEYS.PROVIDER, config.provider);
    }
    
    if (config.model) {
      await this.setCookieValue(COOKIE_KEYS.MODEL, config.model);
    }

    // 保存API密钥（如果提供）
    if (config.apiKey !== undefined) {
      const providerKey = `${COOKIE_KEYS.API_KEY_PREFIX}${newConfig.provider}`;
      if (config.apiKey) {
        await this.setCookieValue(providerKey, config.apiKey);
      } else {
        await this.removeCookieValue(providerKey);
      }
    }

    // 保存基础URL（如果提供）
    if (config.baseUrl !== undefined) {
      const baseUrlKey = `${COOKIE_KEYS.BASE_URL_PREFIX}${newConfig.provider}`;
      if (config.baseUrl) {
        await this.setCookieValue(baseUrlKey, config.baseUrl);
      } else {
        await this.removeCookieValue(baseUrlKey);
      }
    }
    
    // 保存自定义模型（如果提供）
    if (config.customModels !== undefined) {
      if (config.customModels && config.customModels.length > 0) {
        const customModelsStr = JSON.stringify(config.customModels);
        console.log('🔍 保存自定义模型:', customModelsStr);
        await this.setCookieValue(COOKIE_KEYS.CUSTOM_MODELS, customModelsStr);
      } else {
        await this.removeCookieValue(COOKIE_KEYS.CUSTOM_MODELS);
      }
    }
    
    // 保存端点配置（如果提供）
    if (config.endpoints !== undefined) {
      if (config.endpoints && config.endpoints.length > 0) {
        const endpointsStr = JSON.stringify(config.endpoints);
        console.log('🔍 保存端点配置:', endpointsStr);
        await this.setCookieValue(COOKIE_KEYS.ENDPOINTS, endpointsStr);
      } else {
        await this.removeCookieValue(COOKIE_KEYS.ENDPOINTS);
      }
    }
  }

  /**
   * 保存特定提供商的API密钥
   */
  async saveApiKey(provider: ProviderType, apiKey: string): Promise<void> {
    const providerKey = `${COOKIE_KEYS.API_KEY_PREFIX}${provider}`;
    if (apiKey) {
      await this.setCookieValue(providerKey, apiKey);
    } else {
      await this.removeCookieValue(providerKey);
    }
  }

  /**
   * 保存特定提供商的基础URL
   */
  async saveBaseUrl(provider: ProviderType, baseUrl: string): Promise<void> {
    const baseUrlKey = `${COOKIE_KEYS.BASE_URL_PREFIX}${provider}`;
    if (baseUrl) {
      await this.setCookieValue(baseUrlKey, baseUrl);
    } else {
      await this.removeCookieValue(baseUrlKey);
    }
  }

  /**
   * 获取特定提供商的API密钥
   */
  async getApiKey(provider: ProviderType): Promise<string | undefined> {
    const value = await this.getCookieValue(`${COOKIE_KEYS.API_KEY_PREFIX}${provider}`);
    
    // 检查是否是OpenAI提供商，如果是，则API_KEY是必需的
    if (provider === 'openai' && (value === undefined || value === null)) {
      console.error(`❌ ${provider}的API密钥未定义，这是必需的`);
      throw new Error(`${provider}的API密钥未定义，请在设置中配置API密钥`);
    }
    
    // 确保返回字符串或undefined
    if (value === undefined || value === null) {
      return undefined;
    }
    
    // 如果是对象，尝试提取value属性
    if (typeof value === 'object' && value !== null) {
      if ('value' in value) {
        console.log(`API密钥是对象类型，提取value属性`, value);
        return value.value;
      }
      console.log(`API密钥是对象类型，尝试转换为字符串`, value);
      return String(value);
    }
    
    return String(value);
  }

  /**
   * 获取特定提供商的基础URL
   */
  async getBaseUrl(provider: ProviderType): Promise<string | undefined> {
    const value = await this.getCookieValue(`${COOKIE_KEYS.BASE_URL_PREFIX}${provider}`);
    
    // 确保返回字符串或undefined
    if (value === undefined || value === null) {
      return undefined;
    }
    
    // 如果是对象，尝试提取value属性
    if (typeof value === 'object' && value !== null) {
      if ('value' in value) {
        console.log(`基础URL是对象类型，提取value属性`, value);
        return value.value;
      }
      console.log(`基础URL是对象类型，尝试转换为字符串`, value);
      return String(value);
    }
    
    return String(value);
  }
  
  /**
   * 获取自定义模型列表
   */
  async getCustomModels(): Promise<CustomModel[]> {
    const value = await this.getCookieValue(COOKIE_KEYS.CUSTOM_MODELS);
    console.log('🔍 获取自定义模型配置:', value);
    return tryParseJSON<CustomModel[]>(value, []);
  }
  
  /**
   * 获取端点配置列表
   */
  async getEndpoints(): Promise<Endpoint[]> {
    const value = await this.getCookieValue(COOKIE_KEYS.ENDPOINTS);
    console.log('🔍 获取端点配置:', value);
    return tryParseJSON<Endpoint[]>(value, []);
  }

  /**
   * 清除所有配置
   */
  async clearConfig(): Promise<void> {
    await this.removeCookieValue(COOKIE_KEYS.PROVIDER);
    await this.removeCookieValue(COOKIE_KEYS.MODEL);
    
    // 清除所有提供商的API密钥和基础URL
    const providers: ProviderType[] = ['together', 'openai', 'anthropic', 'google', 'alicloud', 'tencent', 'volcanoengine'];
    for (const provider of providers) {
      await this.removeCookieValue(`${COOKIE_KEYS.API_KEY_PREFIX}${provider}`);
      await this.removeCookieValue(`${COOKIE_KEYS.BASE_URL_PREFIX}${provider}`);
    }
    
    // 清除自定义配置
    await this.removeCookieValue(COOKIE_KEYS.CUSTOM_MODELS);
    await this.removeCookieValue(COOKIE_KEYS.ENDPOINTS);
  }

  /**
   * 获取cookie值，支持异步操作
   */
  private async getCookieValue(key: string): Promise<any> {
    console.log(`🔍 获取cookie值: ${key}, 环境: ${isEdge ? 'Edge' : '非Edge'}`);
    if (isEdge) {
      // 在Edge环境中，cookies().get()需要await
      const value = await this.cookies.get(key);
      console.log(`🔍 Edge环境获取cookie值: ${key} = ${JSON.stringify(value)}, 类型: ${typeof value}`);
      return value;
    } else {
      // 在非Edge环境中，NextJS现在也要求使用await
      const value = await this.cookies.get(key);
      console.log(`🔍 非Edge环境获取cookie值: ${key} = ${JSON.stringify(value)}, 类型: ${typeof value}`);
      return value;
    }
  }

  /**
   * 设置cookie值，支持异步操作
   */
  private async setCookieValue(key: string, value: any): Promise<void> {
    // 如果值是对象或数组，转换为JSON字符串
    const cookieValue = typeof value === 'object' ? JSON.stringify(value) : value;
    console.log(`🔍 设置cookie值: ${key} = ${cookieValue}, 类型: ${typeof cookieValue}, 环境: ${isEdge ? 'Edge' : '非Edge'}`);
    
    if (isEdge) {
      // 在Edge环境中，cookies().set()需要await
      await this.cookies.set(key, cookieValue);
    } else {
      // 在非Edge环境中，NextJS现在也要求使用await
      await this.cookies.set(key, cookieValue);
    }
  }

  /**
   * 删除cookie值，支持异步操作
   */
  private async removeCookieValue(key: string): Promise<void> {
    console.log(`🔍 删除cookie值: ${key}, 环境: ${isEdge ? 'Edge' : '非Edge'}`);
    if (isEdge) {
      // 在Edge环境中，cookies().remove()需要await
      await this.cookies.remove(key);
    } else {
      // 在非Edge环境中，NextJS现在也要求使用await
      await this.cookies.remove(key);
    }
  }
}

/**
 * 创建AIConfigManager实例
 */
export function createAIConfigManager(cookies: Cookies): AIConfigManager {
  return new AIConfigManager(cookies);
}
