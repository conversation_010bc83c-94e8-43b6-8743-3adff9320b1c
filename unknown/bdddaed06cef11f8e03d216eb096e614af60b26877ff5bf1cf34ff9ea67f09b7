// 模拟AI回复内容
const mockHtmlResponse = `当然可以！以下是一个简单的"Hello, World!"的HTML示例。

### HTML
\`\`\`html
<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hello World</title>
</head>
<body>
  <h1>Hello, World!</h1>
</body>
</html>
\`\`\`

### Markdown
\`\`\`markdown
# Hello, World!
\`\`\`

你可以根据需要使用这两种格式！如果还有其他需求，请告诉我。`;

const mockMarkdownResponse = `当然可以！以下是一个简单的"Hello, World!"的Markdown示例。

### Markdown
\`\`\`markdown
# Hello, World!

这是一个简单的Markdown文档。

## 二级标题

- 列表项1
- 列表项2
- 列表项3

### 代码示例

\`\`\`javascript
console.log("Hello, World!");
\`\`\`
\`\`\`

你可以根据需要使用这个格式！如果还有其他需求，请告诉我。`;

// 提取HTML代码的函数
function extractHtmlFromConversation(message) {
  console.log('Extracting HTML from message...');
  
  // 尝试提取HTML代码块
  const htmlMatch = message.match(/```html\s*([\s\S]*?)\s*```/);
  if (htmlMatch && htmlMatch[1]) {
    const code = htmlMatch[1].trim();
    console.log('Found HTML code block:', code.substring(0, 50) + '...');
    
    // 检查是否是完整HTML
    if (code.includes('<!DOCTYPE html>') || code.includes('<html')) {
      return code;
    } else {
      // 如果不是完整HTML，包装它
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hello World</title>
</head>
<body>
  ${code}
</body>
</html>`;
    }
  }
  
  // 尝试提取无语言指定的代码块
  const genericMatch = message.match(/```\s*([\s\S]*?)\s*```/);
  if (genericMatch && genericMatch[1]) {
    const code = genericMatch[1].trim();
    console.log('Found generic code block:', code.substring(0, 50) + '...');
    
    // 检查是否是HTML
    if (code.includes('<') && code.includes('>')) {
      // 检查是否是完整HTML
      if (code.includes('<!DOCTYPE html>') || code.includes('<html')) {
        return code;
      } else {
        // 如果不是完整HTML，包装它
        return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hello World</title>
</head>
<body>
  ${code}
</body>
</html>`;
      }
    }
  }
  
  // 尝试直接从消息中提取HTML
  if (message.includes('<!DOCTYPE html>') || message.includes('<html')) {
    // 尝试提取完整的HTML文档
    const docTypeIndex = message.indexOf('<!DOCTYPE html>');
    const htmlStartIndex = message.indexOf('<html');
    const startIndex = docTypeIndex >= 0 ? docTypeIndex : htmlStartIndex;
    
    if (startIndex >= 0) {
      const htmlEndIndex = message.indexOf('</html>', startIndex);
      if (htmlEndIndex >= 0) {
        const html = message.substring(startIndex, htmlEndIndex + 7);
        console.log('Extracted complete HTML document:', html.substring(0, 50) + '...');
        return html;
      }
    }
  }
  
  return null;
}

// 提取Markdown代码的函数
function extractMarkdownFromConversation(message) {
  console.log('Extracting Markdown from message...');
  
  // 尝试提取Markdown代码块
  const mdMatch = message.match(/```markdown\s*([\s\S]*?)\s*```/);
  if (mdMatch && mdMatch[1]) {
    const code = mdMatch[1].trim();
    console.log('Found Markdown code block:', code.substring(0, 50) + '...');
    return code;
  }
  
  // 尝试提取md代码块
  const mdBlockMatch = message.match(/```md\s*([\s\S]*?)\s*```/);
  if (mdBlockMatch && mdBlockMatch[1]) {
    const code = mdBlockMatch[1].trim();
    console.log('Found md code block:', code.substring(0, 50) + '...');
    return code;
  }
  
  // 尝试提取无语言指定的代码块
  const genericMatch = message.match(/```\s*([\s\S]*?)\s*```/);
  if (genericMatch && genericMatch[1]) {
    const code = genericMatch[1].trim();
    console.log('Found generic code block for Markdown:', code.substring(0, 50) + '...');
    return code;
  }
  
  // 如果没有找到代码块，尝试提取以#开头的内容
  if (message.includes('# ')) {
    const lines = message.split('\n');
    const titleLineIndex = lines.findIndex(line => line.trim().startsWith('# '));
    
    if (titleLineIndex >= 0) {
      const markdown = lines.slice(titleLineIndex).join('\n');
      console.log('Extracted Markdown content:', markdown.substring(0, 50) + '...');
      return markdown;
    }
  }
  
  return null;
}

// 测试HTML提取
console.log('=== Testing HTML extraction ===');
const extractedHtml = extractHtmlFromConversation(mockHtmlResponse);
console.log('Extracted HTML:', extractedHtml);
console.log('HTML length:', extractedHtml ? extractedHtml.length : 0);
console.log('\n');

// 测试Markdown提取
console.log('=== Testing Markdown extraction ===');
const extractedMarkdown = extractMarkdownFromConversation(mockMarkdownResponse);
console.log('Extracted Markdown:', extractedMarkdown);
console.log('Markdown length:', extractedMarkdown ? extractedMarkdown.length : 0);

// 测试直接提取HTML
const directHtmlTest = `当然可以！以下是一个简单的HTML页面：

<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hello World</title>
</head>
<body>
  <h1>Hello, World!</h1>
</body>
</html>

希望这对你有帮助！`;

console.log('\n=== Testing direct HTML extraction ===');
const directExtractedHtml = extractHtmlFromConversation(directHtmlTest);
console.log('Direct extracted HTML:', directExtractedHtml);
console.log('Direct HTML length:', directExtractedHtml ? directExtractedHtml.length : 0);
