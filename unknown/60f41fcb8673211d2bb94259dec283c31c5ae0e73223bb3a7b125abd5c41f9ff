"use client";

import { useState } from "react";
import { useCookies } from "next-client-cookies";
import { AIConfigManager } from "@/lib/ai-config";
import { ProviderType } from "@/lib/provider-factory";

// 错误测试类型
type ErrorTestType = 
  | "invalid-api-key" 
  | "invalid-base-url" 
  | "network-error" 
  | "timeout" 
  | "rate-limit";

interface TestResult {
  type: ErrorTestType;
  status: "success" | "error" | "pending";
  message: string;
}

export default function TestErrorPage() {
  const cookies = useCookies();
  const configManager = new AIConfigManager(cookies);
  
  const [apiKey, setApiKey] = useState("");
  const [baseUrl, setBaseUrl] = useState("");
  const [provider, setProvider] = useState<ProviderType>("openai");
  const [model, setModel] = useState("gpt-3.5-turbo");
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [loading, setLoading] = useState(false);
  
  // 保存配置
  const saveConfig = () => {
    configManager.saveConfig({
      provider,
      model,
      apiKey,
      baseUrl
    });
    
    alert("配置已保存");
  };
  
  // 运行错误测试
  const runErrorTest = async (type: ErrorTestType) => {
    // 更新测试结果状态
    setTestResults(prev => [
      ...prev.filter(r => r.type !== type),
      { type, status: "pending", message: "测试中..." }
    ]);
    
    try {
      // 根据测试类型构建请求参数
      const testParams: Record<string, any> = {
        "invalid-api-key": {
          apiKey: "sk-invalid-key-for-testing",
          baseUrl,
          provider,
          model,
        },
        "invalid-base-url": {
          apiKey,
          baseUrl: "https://invalid-api-url.example.com",
          provider,
          model,
        },
        "network-error": {
          apiKey,
          baseUrl,
          provider,
          model,
          simulateNetworkError: true,
        },
        "timeout": {
          apiKey,
          baseUrl,
          provider,
          model,
          simulateTimeout: true,
        },
        "rate-limit": {
          apiKey,
          baseUrl,
          provider,
          model,
          simulateRateLimit: true,
        },
      };
      
      // 发送请求
      const response = await fetch("/api/test-openai-error", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testParams[type]),
      });
      
      // 解析响应
      const data = await response.json();
      
      // 更新测试结果
      if (response.ok) {
        setTestResults(prev => [
          ...prev.filter(r => r.type !== type),
          { 
            type, 
            status: "success", 
            message: "测试成功：错误被正确捕获并处理" 
          }
        ]);
      } else {
        setTestResults(prev => [
          ...prev.filter(r => r.type !== type),
          { 
            type, 
            status: "error", 
            message: `错误信息: ${data.error || "未知错误"}` 
          }
        ]);
      }
    } catch (err) {
      console.error(`测试错误 (${type}):`, err);
      
      // 更新测试结果
      setTestResults(prev => [
        ...prev.filter(r => r.type !== type),
        { 
          type, 
          status: "error", 
          message: `测试过程中出现异常: ${err instanceof Error ? err.message : String(err)}` 
        }
      ]);
    }
  };
  
  // 运行所有错误测试
  const runAllErrorTests = async () => {
    setLoading(true);
    setTestResults([]);
    
    const errorTypes: ErrorTestType[] = [
      "invalid-api-key",
      "invalid-base-url",
      "network-error",
      "timeout",
      "rate-limit"
    ];
    
    // 依次运行所有测试
    for (const type of errorTypes) {
      await runErrorTest(type);
    }
    
    setLoading(false);
  };
  
  // 获取测试结果的显示样式
  const getResultStyle = (status: "success" | "error" | "pending") => {
    switch (status) {
      case "success":
        return "bg-green-50 border-green-200 text-green-700";
      case "error":
        return "bg-red-50 border-red-200 text-red-700";
      case "pending":
        return "bg-yellow-50 border-yellow-200 text-yellow-700";
    }
  };
  
  // 获取测试结果的图标
  const getResultIcon = (status: "success" | "error" | "pending") => {
    switch (status) {
      case "success":
        return "✅";
      case "error":
        return "❌";
      case "pending":
        return "⏳";
    }
  };
  
  // 获取测试类型的显示名称
  const getTestTypeName = (type: ErrorTestType) => {
    const names: Record<ErrorTestType, string> = {
      "invalid-api-key": "无效的API密钥",
      "invalid-base-url": "无效的基础URL",
      "network-error": "网络错误",
      "timeout": "请求超时",
      "rate-limit": "速率限制"
    };
    
    return names[type] || type;
  };
  
  return (
    <>
      <h1 className="text-2xl font-bold mb-6">OpenAI API集成 - 错误处理测试</h1>
      
      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-4">API配置</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              提供商
            </label>
            <select
              value={provider}
              onChange={(e) => setProvider(e.target.value as ProviderType)}
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            >
              <option value="openai">OpenAI</option>
              <option value="together">OpenAI Compatible</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模型
            </label>
            <select
              value={model}
              onChange={(e) => setModel(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            >
              <option value="gpt-4o">GPT-4o</option>
              <option value="gpt-4-turbo">GPT-4 Turbo</option>
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
              <option value="grok-3-beta">grok-3-beta</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              API密钥
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="输入OpenAI API密钥"
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              基础URL (可选)
            </label>
            <input
              type="text"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              placeholder="输入自定义API地址"
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            />
          </div>
          
          <button
            onClick={saveConfig}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            保存配置
          </button>
        </div>
      </div>
      
      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-4">错误处理测试</h2>
        
        <div className="space-y-4">
          <p className="text-gray-600">
            这些测试将模拟各种错误情况，以验证应用程序是否能够正确处理这些错误。
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => runErrorTest("invalid-api-key")}
              disabled={loading}
              className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
            >
              测试无效的API密钥
            </button>
            
            <button
              onClick={() => runErrorTest("invalid-base-url")}
              disabled={loading}
              className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
            >
              测试无效的基础URL
            </button>
            
            <button
              onClick={() => runErrorTest("network-error")}
              disabled={loading}
              className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
            >
              测试网络错误
            </button>
            
            <button
              onClick={() => runErrorTest("timeout")}
              disabled={loading}
              className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
            >
              测试请求超时
            </button>
            
            <button
              onClick={() => runErrorTest("rate-limit")}
              disabled={loading}
              className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600"
            >
              测试速率限制
            </button>
          </div>
          
          <button
            onClick={runAllErrorTests}
            disabled={loading}
            className={`w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {loading ? "测试中..." : "运行所有错误测试"}
          </button>
        </div>
      </div>
      
      {testResults.length > 0 && (
        <div className="bg-gray-50 p-6 rounded-lg mb-6">
          <h2 className="text-lg font-semibold mb-4">测试结果</h2>
          
          <div className="space-y-4">
            {testResults.map((result) => (
              <div
                key={result.type}
                className={`p-4 rounded border ${getResultStyle(result.status)}`}
              >
                <div className="flex items-start">
                  <span className="mr-2">{getResultIcon(result.status)}</span>
                  <div>
                    <h3 className="font-medium">{getTestTypeName(result.type)}</h3>
                    <p>{result.message}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="mt-8">
        <a 
          href="/test-openai"
          className="inline-block px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          返回测试首页
        </a>
      </div>
    </>
  );
}
