/**
 * Anthropic Provider实现
 * 支持Claude模型
 */

import Anthropic from '@anthropic-ai/sdk';
import { BaseProvider, ChatCompletionOptions, ChatCompletionResponse, Message, ProviderConfig } from './base';
import { getModelById } from '../models';

export class AnthropicProvider extends BaseProvider {
  private client: Anthropic | null = null;

  constructor(config: ProviderConfig) {
    super('anthropic', config);
    this.initClient();
  }

  /**
   * 初始化Anthropic客户端
   */
  private initClient(): void {
    try {
      if (!this.validateConfig()) {
        console.warn('Anthropic配置不完整，将使用默认配置');
        // 使用默认配置而不是抛出错误
        this.client = new Anthropic({
          apiKey: this.config.apiKey || 'sk-placeholder',
          baseURL: this.config.baseUrl,
        });
        return;
      }

      this.client = new Anthropic({
        apiKey: this.config.apiKey,
        baseURL: this.config.baseUrl,
      });
    } catch (error) {
      console.error('初始化Anthropic客户端失败:', error);
      this.client = null;
    }
  }

  /**
   * 验证配置
   */
  validateConfig(): boolean {
    // 允许没有API密钥，但会在日志中显示警告
    return true;
  }

  /**
   * 转换消息格式
   * Anthropic使用的消息格式与OpenAI略有不同
   */
  private convertMessages(messages: Message[]): any[] {
    // 简化消息转换，只保留用户和助手角色
    return messages.map(msg => {
      let role = 'user';
      if (msg.role === 'assistant') {
        role = 'assistant';
      } else if (msg.role === 'system') {
        // 系统消息需要特殊处理，这里简化为用户消息
        role = 'user';
      }
      
      return {
        role,
        content: msg.content
      };
    });
  }

  /**
   * 聊天完成方法
   */
  async chatCompletion(options: ChatCompletionOptions): Promise<ChatCompletionResponse> {
    if (!this.client) {
      this.initClient();
      if (!this.client) {
        throw new Error('Anthropic客户端未初始化');
      }
    }

    try {
      const modelConfig = getModelById(options.model);
      if (!modelConfig) {
        throw new Error(`未找到模型配置: ${options.model}`);
      }

      // 检查是否有有效的API密钥
      if (!this.config.apiKey || this.config.apiKey === 'sk-placeholder') {
        return {
          content: '请在AI设置中配置Anthropic API密钥后再使用此模型。',
          model: options.model,
          usage: {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0
          }
        };
      }

      // 提取系统消息
      let systemPrompt = '';
      const nonSystemMessages = [];
      for (const msg of options.messages) {
        if (msg.role === 'system') {
          systemPrompt = msg.content;
        } else {
          nonSystemMessages.push(msg);
        }
      }

      const response = await this.client.messages.create({
        model: options.model,
        messages: this.convertMessages(nonSystemMessages),
        system: systemPrompt,
        temperature: options.temperature ?? modelConfig.temperature ?? 0.7,
        max_tokens: options.maxTokens ?? modelConfig.maxTokens ?? 4096,
      });

      return {
        content: response.content && response.content[0] && 'text' in response.content[0] 
          ? (response.content[0].text as string) || '' 
          : '',
        model: options.model,
        usage: {
          promptTokens: response.usage?.input_tokens || 0,
          completionTokens: response.usage?.output_tokens || 0,
          totalTokens: (response.usage?.input_tokens || 0) + (response.usage?.output_tokens || 0)
        }
      };
    } catch (error) {
      console.error('Anthropic聊天完成失败:', error);
      throw error;
    }
  }

  /**
   * 流式聊天完成方法
   */
  async streamingChatCompletion(
    options: ChatCompletionOptions,
    onContent: (content: string) => void,
    onError: (error: Error) => void,
    onFinish: (response: ChatCompletionResponse) => void
  ): Promise<void> {
    if (!this.client) {
      this.initClient();
      if (!this.client) {
        onError(new Error('Anthropic客户端未初始化'));
        return;
      }
    }

    try {
      const modelConfig = getModelById(options.model);
      if (!modelConfig) {
        onError(new Error(`未找到模型配置: ${options.model}`));
        return;
      }

      // 检查是否有有效的API密钥
      if (!this.config.apiKey || this.config.apiKey === 'sk-placeholder') {
        onContent('请在AI设置中配置Anthropic API密钥后再使用此模型。');
        onFinish({
          content: '请在AI设置中配置Anthropic API密钥后再使用此模型。',
          model: options.model,
          usage: {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0
          }
        });
        return;
      }

      // 提取系统消息
      let systemPrompt = '';
      const nonSystemMessages = [];
      for (const msg of options.messages) {
        if (msg.role === 'system') {
          systemPrompt = msg.content;
        } else {
          nonSystemMessages.push(msg);
        }
      }

      const stream = await this.client.messages.create({
        model: options.model,
        messages: this.convertMessages(nonSystemMessages),
        system: systemPrompt,
        temperature: options.temperature ?? modelConfig.temperature ?? 0.7,
        max_tokens: options.maxTokens ?? modelConfig.maxTokens ?? 4096,
        stream: true
      });

      let fullContent = '';
      let usage = {
        promptTokens: 0,
        completionTokens: 0,
        totalTokens: 0
      };

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta && 'text' in chunk.delta) {
          const text = chunk.delta.text as string;
          fullContent += text;
          onContent(text);
        } else if (chunk.type === 'message_stop') {
          // 消息结束，尝试获取使用情况
          if ('usage' in chunk && chunk.usage) {
            const chunkUsage = chunk.usage as any;
            usage = {
              promptTokens: chunkUsage.input_tokens || 0,
              completionTokens: chunkUsage.output_tokens || 0,
              totalTokens: (chunkUsage.input_tokens || 0) + (chunkUsage.output_tokens || 0)
            };
          }
        }
      }

      // 流式响应完成
      onFinish({
        content: fullContent,
        model: options.model,
        usage
      });
    } catch (error) {
      console.error('Anthropic流式聊天完成失败:', error);
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  }
}
