/**
 * 提示词模块类型定义
 */

/**
 * 提示词模块接口
 */
export interface PromptModule {
  id: string;           // 模块唯一标识
  name: string;         // 模块名称
  description: string;  // 模块描述
  content: string;      // 模块内容（XML格式）
  dependencies?: string[]; // 依赖的其他模块ID
  isCore?: boolean;     // 是否为核心模块（默认加载）
  order?: number;       // 模块加载顺序（数字越小越先加载）
}

/**
 * 提示词模块注册表
 */
export interface PromptModuleRegistry {
  [moduleId: string]: PromptModule;
}

/**
 * 提示词组装选项
 */
export interface PromptAssemblyOptions {
  includeModules?: string[];  // 要包含的模块ID列表
  excludeModules?: string[];  // 要排除的模块ID列表
  customModules?: PromptModule[]; // 自定义模块列表
  variables?: Record<string, string>; // 变量替换
}
