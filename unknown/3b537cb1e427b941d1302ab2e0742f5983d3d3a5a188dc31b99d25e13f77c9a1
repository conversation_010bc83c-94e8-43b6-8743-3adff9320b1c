#!/bin/bash
# 构建+部署打包脚本（适用于方案二/常规部署，Linux/Mac 版）
set -e

# 1. 安装依赖
echo "==> 安装依赖..."
npm install

# 2. 构建生产包
echo "==> 构建生产包..."
npm run build

# 3. 复制部署所需文件到 deploy-dist 目录
echo "==> 复制部署文件到 deploy-dist ..."
rm -rf deploy-dist
mkdir -p deploy-dist
cp -r .next package.json node_modules public deploy-dist/
[ -f next.config.js ] && cp next.config.js deploy-dist/
[ -f package-lock.json ] && cp package-lock.json deploy-dist/

echo "✅ 构建完成，部署文件已生成在 deploy-dist 目录"
