/**
 * 提示词模块注册表
 */
import { PromptModule, PromptModuleRegistry } from './types';
import { coreModules } from './core-modules';
import { featureModules } from './feature-modules';

/**
 * 全局提示词模块注册表
 */
class PromptModuleRegistryManager {
  private registry: PromptModuleRegistry = {};

  /**
   * 构造函数 - 初始化注册表
   */
  constructor() {
    // 注册核心模块
    this.registerModules(coreModules);
    
    // 注册功能模块
    this.registerModules(featureModules);
  }

  /**
   * 注册单个模块
   * @param promptModule 要注册的模块
   */
  registerModule(promptModule: PromptModule): void {
    if (this.registry[promptModule.id]) {
      console.warn(`模块 ${promptModule.id} 已存在，将被覆盖`);
    }
    this.registry[promptModule.id] = promptModule;
  }

  /**
   * 注册多个模块
   * @param modules 要注册的模块数组
   */
  registerModules(modules: PromptModule[]): void {
    modules.forEach(promptModule => this.registerModule(promptModule));
  }

  /**
   * 获取模块
   * @param moduleId 模块ID
   * @returns 模块对象，如果不存在则返回undefined
   */
  getModule(moduleId: string): PromptModule | undefined {
    return this.registry[moduleId];
  }

  /**
   * 获取所有模块
   * @returns 所有模块的数组
   */
  getAllModules(): PromptModule[] {
    return Object.values(this.registry);
  }

  /**
   * 获取核心模块
   * @returns 核心模块的数组
   */
  getCoreModules(): PromptModule[] {
    return Object.values(this.registry).filter(module => module.isCore);
  }

  /**
   * 获取功能模块
   * @returns 功能模块的数组
   */
  getFeatureModules(): PromptModule[] {
    return Object.values(this.registry).filter(module => !module.isCore);
  }

  /**
   * 获取模块依赖
   * @param moduleId 模块ID
   * @returns 依赖模块的数组
   */
  getModuleDependencies(moduleId: string): PromptModule[] {
    const promptModule = this.getModule(moduleId);
    if (!promptModule || !promptModule.dependencies || promptModule.dependencies.length === 0) {
      return [];
    }

    return promptModule.dependencies
      .map(depId => this.getModule(depId))
      .filter((dep): dep is PromptModule => !!dep);
  }

  /**
   * 获取所有依赖模块（递归）
   * @param moduleIds 模块ID数组
   * @returns 所有依赖模块的数组（包括传入的模块）
   */
  getAllDependencies(moduleIds: string[]): PromptModule[] {
    const result = new Set<PromptModule>();
    const processedIds = new Set<string>();

    const processDependencies = (ids: string[]) => {
      ids.forEach(id => {
        if (processedIds.has(id)) return;
        processedIds.add(id);

        const promptModule = this.getModule(id);
        if (!promptModule) return;

        result.add(promptModule);

        if (promptModule.dependencies && promptModule.dependencies.length > 0) {
          processDependencies(promptModule.dependencies);
        }
      });
    };

    processDependencies(moduleIds);
    return Array.from(result);
  }
}

/**
 * 全局提示词模块注册表实例
 */
export const promptModuleRegistry = new PromptModuleRegistryManager();
