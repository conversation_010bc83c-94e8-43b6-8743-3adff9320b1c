# 内容生成器组件技术文档

## 1. 概述

内容生成器是一个支持代码展示以及HTML和Markdown渲染的组件，允许用户通过对话方式生成内容并实时预览。该组件能够自动从AI回复中提取代码，并在右侧面板中显示渲染结果。

## 2. 主要功能

- **对话式内容生成**：通过与AI对话生成HTML或Markdown内容
- **代码自动提取**：从AI回复中自动提取HTML或Markdown代码
- **实时预览**：实时显示生成内容的渲染效果
- **多视图模式**：支持代码、预览和分屏视图模式
- **多模型支持**：支持选择不同的AI模型进行对话

## 3. 技术架构

### 3.1 核心组件

- **ContentGeneratorClient**：内容生成器的主客户端组件
- **ConversationPanel**：左侧对话面板组件
- **ContentViewerPanel**：右侧内容查看器面板组件
- **FileViewer**：文件查看器组件
- **ContentViewer**：内容查看器组件
- **HtmlPreview**：HTML预览组件
- **MarkdownPreview**：Markdown预览组件

### 3.2 工具函数

- **extractCodeFromMessage**：从消息中提取代码的工具函数
- **extractHtmlFromMessage**：从消息中提取HTML代码的工具函数
- **extractMarkdownFromMessage**：从消息中提取Markdown代码的工具函数

### 3.3 API端点

- **/api/chat**：处理对话请求的API端点
- **/api/stream-content**：处理流式内容生成的API端点

## 4. 工作流程

1. **用户输入**：用户在左侧对话面板中输入提示
2. **AI回复**：系统调用AI模型生成回复
3. **代码提取**：系统从AI回复中自动提取代码
4. **内容生成**：系统使用提取的代码生成内容
5. **实时预览**：系统在右侧面板中显示渲染效果

## 5. 代码提取机制

### 5.1 HTML代码提取

系统使用多种方法提取HTML代码：

1. **代码块提取**：提取````html ... ```格式的代码块
2. **完整HTML文档提取**：直接提取包含`<!DOCTYPE html>`和`</html>`的完整HTML文档
3. **通用代码块提取**：提取包含HTML标签的通用代码块

如果提取的代码不是完整的HTML文档，系统会自动包装它：

```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hello World</title>
</head>
<body>
  <!-- 提取的代码 -->
</body>
</html>
```

### 5.2 Markdown代码提取

系统使用多种方法提取Markdown代码：

1. **代码块提取**：提取````markdown ... ```或````md ... ```格式的代码块
2. **通用代码块提取**：提取不包含HTML标签的通用代码块
3. **标题提取**：提取以`#`开头的内容

## 6. 预览机制

### 6.1 HTML预览

HTML预览使用iframe实现，支持以下功能：

- **自动包装**：自动包装不完整的HTML文档
- **样式隔离**：使用iframe确保样式隔离
- **链接处理**：处理iframe内部链接，防止导航离开当前页面
- **错误处理**：在渲染失败时显示原始内容

### 6.2 Markdown预览

Markdown预览使用React-Markdown实现，支持以下功能：

- **GFM支持**：支持GitHub风格的Markdown
- **代码高亮**：支持代码块语法高亮
- **动态加载**：动态加载依赖，减少初始加载时间
- **降级渲染**：在依赖加载前使用简单的Markdown渲染

## 7. 使用指南

### 7.1 基本使用

1. 在左侧对话面板中输入提示，例如"写个hello world"
2. AI会生成回复，包含HTML或Markdown代码
3. 系统自动从回复中提取代码，并在右侧面板中显示渲染效果
4. 可以使用视图模式按钮切换代码、预览和分屏视图

### 7.2 高级选项

- **内容类型**：选择生成HTML或Markdown内容
- **模型选择**：选择不同的AI模型进行对话
- **样式选择**：选择生成内容的样式
- **复杂度选择**：选择生成内容的复杂度

## 8. 技术实现细节

### 8.1 代码提取

代码提取使用正则表达式实现，支持多种格式的代码块：

```typescript
// 提取HTML代码块
const htmlMatch = message.match(/```html\s*([\s\S]*?)\s*```/);

// 提取完整HTML文档
if (message.includes('<!DOCTYPE html>') && message.includes('</html>')) {
  const startIndex = message.indexOf('<!DOCTYPE html>');
  const endIndex = message.indexOf('</html>', startIndex) + 7;
  const html = message.substring(startIndex, endIndex);
}

// 提取Markdown代码块
const mdMatch = message.match(/```markdown\s*([\s\S]*?)\s*```/);
```

### 8.2 自动更新

系统使用React的useEffect钩子监听对话变化，自动提取代码并更新生成的文件：

```typescript
useEffect(() => {
  const assistantMessages = conversation.messages.filter(msg => msg.role === 'assistant');
  if (assistantMessages.length > 0) {
    const lastMessage = assistantMessages[assistantMessages.length - 1].content;
    
    // 检查是否包含代码块
    const hasHtmlCode = lastMessage.includes('```html') || 
                      lastMessage.includes('<!DOCTYPE html') ||
                      lastMessage.includes('<html');
    const hasMarkdownCode = lastMessage.includes('```markdown') || 
                          lastMessage.includes('```md') ||
                          lastMessage.includes('# ');
    
    if (hasHtmlCode || hasMarkdownCode) {
      const code = extractCodeFromConversation();
      if (code) {
        // 自动更新生成的文件
        setGeneratedFiles(prev => {
          // 更新文件内容
        });
      }
    }
  }
}, [conversation.messages, extractCodeFromConversation, contentType]);
```

### 8.3 预览实现

HTML预览使用iframe实现，确保样式隔离：

```typescript
useEffect(() => {
  if (!content || !iframeRef.current) return;
  
  const iframe = iframeRef.current;
  const doc = iframe.contentDocument || iframe.contentWindow?.document;
  
  if (!doc) return;
  
  try {
    // 确保内容是完整的HTML文档
    let htmlContent = content;
    if (!content.includes('<!DOCTYPE html>') && !content.includes('<html')) {
      htmlContent = `<!DOCTYPE html>
<html>
<head>
  <!-- 头部内容 -->
</head>
<body>
  ${content}
</body>
</html>`;
    }
    
    // 写入内容到iframe
    doc.open();
    doc.write(htmlContent);
    doc.close();
    
    // 处理iframe内部链接
    const links = doc.querySelectorAll('a');
    links.forEach(link => {
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
    });
  } catch (error) {
    // 错误处理
  }
}, [content]);
```

## 9. 故障排除

### 9.1 常见问题

1. **内容不显示**：检查代码提取是否成功，查看控制台日志
2. **预览不正确**：检查提取的代码是否完整，可能需要手动编辑
3. **流式输出不工作**：检查网络连接和API端点状态

### 9.2 调试技巧

- 使用浏览器开发者工具查看控制台日志
- 检查网络请求和响应
- 查看代码提取和预览组件的日志输出

## 10. 未来改进

### 10.1 功能增强

- **多文件支持**：支持从一个对话中提取多个代码块，生成多个文件
- **代码编辑**：允许用户编辑提取的代码
- **历史记录**：保存生成的内容历史记录
- **导出功能**：支持导出生成的内容为文件

### 10.2 性能优化

- **减少不必要的渲染**：优化组件渲染逻辑
- **懒加载组件**：使用懒加载减少初始加载时间
- **缓存机制**：缓存生成的内容，减少重复生成

### 10.3 用户体验改进

- **进度指示器**：添加生成进度指示器
- **错误提示**：提供更友好的错误提示
- **主题支持**：支持明暗主题切换
- **响应式设计**：优化移动设备体验

## 11. 结论

内容生成器组件提供了一种直观的方式，通过对话生成HTML和Markdown内容并实时预览。它的核心功能是自动从AI回复中提取代码，并在右侧面板中显示渲染效果，大大提高了内容创建的效率。

通过持续改进代码提取和预览机制，该组件能够处理各种格式的代码块，并提供准确的渲染效果。未来的改进将进一步增强其功能和用户体验，使其成为更强大的内容创建工具。
