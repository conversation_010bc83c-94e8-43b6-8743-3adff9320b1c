# 内容生成器文档

本目录包含内容生成器组件的相关文档。内容生成器是一个支持代码展示以及HTML和Markdown渲染的组件，允许用户通过对话方式生成内容并实时预览。

## 文档索引

- [技术文档](content-generator-technical-doc.md) - 详细的技术实现文档，适合开发人员阅读
- [用户指南](content-generator-user-guide.md) - 面向用户的使用指南，包含基本操作和高级功能

## 功能概述

内容生成器提供以下核心功能：

- 通过对话方式生成HTML或Markdown内容
- 自动从AI回复中提取代码
- 实时预览渲染效果
- 支持代码、预览和分屏视图模式
- 支持选择不同的AI模型

## 快速链接

- [内容生成器应用](http://localhost:4000/content-generator) - 访问内容生成器应用
- [项目源代码](../app/content-generator/) - 查看内容生成器的源代码

## 贡献

如果您想为内容生成器做出贡献，请参考[技术文档](content-generator-technical-doc.md)了解项目架构和实现细节。

## 反馈

如有任何问题或建议，请提交issue或联系项目维护者。
