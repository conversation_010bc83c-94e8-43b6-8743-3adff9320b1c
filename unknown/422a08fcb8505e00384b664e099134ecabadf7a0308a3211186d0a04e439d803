# Sandpack局域网访问问题分析与解决方案

## 问题描述

代码预览功能在localhost:4000下可以正确预览，但在局域网IP地址（************:4000）下无法正确预览，出现以下错误：

```
Couldn't connect to server

This means sandpack cannot connect to the runtime or your network is having some issues. 
Please check the network tab in your browser and try again. 
If the problem persists, report it via email or submit an issue on GitHub.

ENV: create-react-app
ERROR: TIME_OUT
```

## 问题分析

通过查看代码和配置，我发现了以下几个可能导致问题的原因：

1. **CORS 配置限制**：
   - `next.config.ts` 文件中的CORS配置只允许特定域名（`http://easycoder.towards-agi.cn`）的访问
   - 这会阻止从局域网IP（如************）访问时的跨域请求

2. **Sandpack bundlerURL 配置**：
   - 当前代码中使用的是固定的远程bundler URL：`https://sandpack-bundler.vercel.app`
   - 当通过局域网IP访问时，与此远程bundler的通信可能受到限制

3. **网络连接超时**：
   - Sandpack组件内部使用WebSocket进行实时通信
   - 在局域网环境中，这些连接可能受到限制或超时

## 解决方案

### 1. 修改CORS配置

更新`next.config.ts`中的CORS配置，使其允许从局域网IP访问：

```typescript
const nextConfig: NextConfig = {
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          { key: "Access-Control-Allow-Credentials", value: "true" },
          { key: "Access-Control-Allow-Origin", value: "*" }, // 开发环境允许所有来源
          // 或者使用具体的局域网IP: 
          // { key: "Access-Control-Allow-Origin", value: "http://************:4000, http://easycoder.towards-agi.cn" },
          { key: "Access-Control-Allow-Methods", value: "GET,OPTIONS,PATCH,DELETE,POST,PUT" },
          { key: "Access-Control-Allow-Headers", 
            value: "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version" 
          },
        ],
      },
    ];
  },
  // 其他配置保持不变...
};
```

### 2. 调整Sandpack组件配置

修改`components/code-runner-react.tsx`和`components/test-sandpack.tsx`中的Sandpack配置，使用动态或更兼容的bundlerURL设置：

```typescript
// 修改前
options={{
  bundlerURL: "https://sandpack-bundler.vercel.app",
  // 其他选项...
}}

// 修改后 - 方案1：相对路径
options={{
  bundlerURL: "/bundler", // 使用相对路径
  // 其他选项...
}}

// 修改后 - 方案2：动态URL
options={{
  bundlerURL: typeof window !== "undefined" ? 
    `${window.location.protocol}//${window.location.hostname}:4000/bundler` : 
    "https://sandpack-bundler.vercel.app",
  // 其他选项...
}}
```

### 3. 设置Sandpack超时和重试配置

增加超时设置和自动重试机制：

```typescript
options={{
  bundlerURL: "https://sandpack-bundler.vercel.app",
  timeout: 30000, // 增加超时时间到30秒
  autorun: true,
  recompileMode: "delayed",
  recompileDelay: 500,
  // 其他选项...
}}
```

### 4. 确保服务器正确绑定

修改`package.json`中的开发启动脚本，确保服务器绑定到所有网络接口：

```json
"scripts": {
  "dev": "next dev -p 4000 -H 0.0.0.0",
  // 其他脚本...
}
```

## 实施顺序

1. 首先修改CORS配置
2. 然后更新Sandpack组件配置
3. 最后修改服务器启动配置
4. 重启服务并测试局域网访问

## 风险评估

- CORS配置放宽可能带来安全风险，生产环境应严格限制
- 动态bundlerURL可能需要额外的环境变证和条件逻辑
- 需要确保任何修改不会影响localhost下的正常功能