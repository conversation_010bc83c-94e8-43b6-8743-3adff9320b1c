# HTML预览交互式编辑技术方案调研

## 1. 需求概述

为HTML预览组件(`components/content-viewer/html-preview.tsx`)添加以下交互式编辑功能：

- 通过鼠标拖拽修改元素位置
- 通过键盘输入修改文本内容
- 保持与现有Next.js架构的兼容性
- 提供良好的用户体验和性能

## 2. 现状分析

当前HTML预览组件使用iframe实现，主要功能是：

- 渲染用户提供的HTML内容
- 处理不完整的HTML文档（自动添加DOCTYPE、html、head、body等标签）
- 设置iframe样式和安全策略
- 处理iframe内部链接（添加target="_blank"和rel属性）
- 添加基本样式

目前组件不支持任何交互式编辑功能。

## 3. 技术方案调研

### 3.1 GrapesJS

**概述**：GrapesJS是一个开源的Web Builder框架，无需任何依赖，专为创建HTML模板编辑器而设计。

**优势**：
- 完整的拖放界面
- 设备响应式设计
- 组件和模板管理
- 代码查看器（HTML/CSS）
- 资产管理器
- 可自定义的样式管理器
- 支持嵌套组件
- 丰富的插件生态系统

**劣势**：
- 学习曲线较陡峭
- 完整集成可能需要大量工作
- 体积较大（约130KB gzipped）

**集成难度**：中等到高

**示例代码**：
```typescript
import React, { useEffect, useRef } from 'react';
import grapesjs from 'grapesjs';
import 'grapesjs/dist/css/grapes.min.css';

const GrapesJSEditor: React.FC<{ content: string, onChange: (html: string) => void }> = ({ 
  content, 
  onChange 
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!editorRef.current) return;
    
    const editor = grapesjs.init({
      container: editorRef.current,
      fromElement: false,
      components: content,
      height: '100%',
      width: 'auto',
      storageManager: false,
      panels: { defaults: [] },
      deviceManager: { devices: [] },
      plugins: ['gjs-preset-webpage'],
    });
    
    editor.on('update', () => {
      onChange(editor.getHtml());
    });
    
    return () => {
      editor.destroy();
    };
  }, [content, onChange]);
  
  return <div ref={editorRef} style={{ height: '100%', width: '100%' }} />;
};

export default GrapesJSEditor;
```

**参考资料**：
- [GrapesJS官网](https://grapesjs.com/)
- [GitHub仓库](https://github.com/GrapesJS/grapesjs)

### 3.2 ContentEditable + ResizeObserver

**概述**：使用浏览器原生的contentEditable属性结合ResizeObserver和拖拽事件实现轻量级编辑功能。

**优势**：
- 轻量级，无需额外依赖
- 与现有架构集成简单
- 可完全自定义实现
- 性能优良

**劣势**：
- 需要手动处理许多边缘情况
- 跨浏览器兼容性问题需要额外处理
- 功能相对有限，复杂功能需要大量自定义代码

**集成难度**：低到中等

**示例代码**：
```typescript
import React, { useRef, useEffect } from 'react';

const EditableHtmlPreview: React.FC<{ content: string, onChange: (html: string) => void }> = ({
  content,
  onChange
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  useEffect(() => {
    if (!content || !iframeRef.current) return;
    
    const iframe = iframeRef.current;
    const doc = iframe.contentDocument || iframe.contentWindow?.document;
    
    if (!doc) return;
    
    // 设置文档内容
    doc.open();
    doc.write(content);
    doc.close();
    
    // 使所有元素可编辑
    const makeEditable = () => {
      const allElements = doc.querySelectorAll('body *');
      allElements.forEach(el => {
        // 文本编辑
        el.setAttribute('contenteditable', 'true');
        
        // 拖拽功能
        el.setAttribute('draggable', 'true');
        
        // 添加样式以显示可编辑状态
        (el as HTMLElement).style.cursor = 'pointer';
        (el as HTMLElement).style.outline = '1px dashed transparent';
        (el as HTMLElement).style.transition = 'outline 0.2s';
        
        el.addEventListener('mouseover', () => {
          (el as HTMLElement).style.outline = '1px dashed blue';
        });
        
        el.addEventListener('mouseout', () => {
          (el as HTMLElement).style.outline = '1px dashed transparent';
        });
        
        // 拖拽事件
        el.addEventListener('dragstart', (e) => {
          const target = e.target as HTMLElement;
          e.dataTransfer?.setData('text/plain', '');
          target.style.opacity = '0.5';
        });
        
        el.addEventListener('dragend', (e) => {
          const target = e.target as HTMLElement;
          target.style.opacity = '1';
          onChange(doc.documentElement.outerHTML);
        });
      });
      
      // 允许放置
      doc.body.addEventListener('dragover', (e) => {
        e.preventDefault();
      });
      
      doc.body.addEventListener('drop', (e) => {
        e.preventDefault();
        const target = e.target as HTMLElement;
        const x = e.clientX;
        const y = e.clientY;
        
        // 获取最接近的元素
        const elemBelow = doc.elementFromPoint(x, y) as HTMLElement;
        if (elemBelow && elemBelow !== doc.body) {
          // 根据位置决定是放在元素内部还是相邻位置
          const rect = elemBelow.getBoundingClientRect();
          const centerY = rect.top + rect.height / 2;
          
          if (y < centerY) {
            elemBelow.parentNode?.insertBefore(target, elemBelow);
          } else {
            elemBelow.parentNode?.insertBefore(target, elemBelow.nextSibling);
          }
          
          onChange(doc.documentElement.outerHTML);
        }
      });
      
      // 监听内容变化
      doc.body.addEventListener('input', () => {
        onChange(doc.documentElement.outerHTML);
      });
    };
    
    // 确保DOM完全加载
    if (doc.readyState === 'complete') {
      makeEditable();
    } else {
      doc.addEventListener('DOMContentLoaded', makeEditable);
    }
    
    return () => {
      doc.removeEventListener('DOMContentLoaded', makeEditable);
    };
  }, [content, onChange]);
  
  return (
    <iframe
      ref={iframeRef}
      style={{ width: '100%', height: '100%', border: 'none' }}
      title="Editable HTML Preview"
      sandbox="allow-same-origin allow-scripts"
    />
  );
};

export default EditableHtmlPreview;
```

### 3.3 React DnD + Draft.js

**概述**：结合React DnD（拖放库）和Draft.js（富文本编辑库）实现编辑功能。

**优势**：
- 与React生态系统完美集成
- 强大的拖放功能
- 丰富的富文本编辑能力
- 良好的类型支持（TypeScript）

**劣势**：
- 需要多个依赖
- 实现复杂，特别是对于嵌套结构
- Draft.js主要针对文本编辑，对于复杂HTML结构支持有限

**集成难度**：中等

**示例代码**：
```typescript
import React, { useState } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Editor, EditorState, ContentState } from 'draft-js';
import 'draft-js/dist/Draft.css';

// 可拖拽元素组件
const DraggableElement = ({ id, type, children, onMove }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'ELEMENT',
    item: { id, type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  
  return (
    <div
      ref={drag}
      style={{
        opacity: isDragging ? 0.5 : 1,
        cursor: 'move',
        border: '1px dashed #ccc',
        padding: '8px',
        margin: '4px',
      }}
    >
      {children}
    </div>
  );
};

// 可放置区域组件
const DroppableArea = ({ onDrop, children }) => {
  const [{ isOver }, drop] = useDrop({
    accept: 'ELEMENT',
    drop: (item, monitor) => {
      const delta = monitor.getDifferenceFromInitialOffset();
      const x = Math.round(delta.x);
      const y = Math.round(delta.y);
      onDrop(item.id, x, y);
      return undefined;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });
  
  return (
    <div
      ref={drop}
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        backgroundColor: isOver ? 'rgba(0,255,0,0.1)' : 'transparent',
      }}
    >
      {children}
    </div>
  );
};

// 可编辑文本组件
const EditableText = ({ content, onChange }) => {
  const [editorState, setEditorState] = useState(
    EditorState.createWithContent(ContentState.createFromText(content))
  );
  
  const handleChange = (state) => {
    setEditorState(state);
    onChange(state.getCurrentContent().getPlainText());
  };
  
  return (
    <div style={{ padding: '4px', border: '1px solid #eee' }}>
      <Editor editorState={editorState} onChange={handleChange} />
    </div>
  );
};

// 主编辑器组件
const ReactDndEditor = ({ content, onChange }) => {
  // 这里需要解析HTML并转换为React组件树
  // 简化示例，实际实现需要HTML解析器
  
  return (
    <DndProvider backend={HTML5Backend}>
      <DroppableArea onDrop={(id, x, y) => {
        // 处理元素移动逻辑
        console.log(`Moving element ${id} by (${x}, ${y})`);
        // 更新内容并调用onChange
      }}>
        {/* 渲染可拖拽和可编辑的组件 */}
      </DroppableArea>
    </DndProvider>
  );
};

export default ReactDndEditor;
```

### 3.4 Craft.js

**概述**：Craft.js是一个React框架，用于构建可拖放页面编辑器。

**优势**：
- 专为React设计
- 高度可定制
- 用户友好的拖放界面
- 支持组件嵌套
- 良好的TypeScript支持
- 活跃的社区和维护

**劣势**：
- 相对较新，生态系统较小
- 学习曲线较陡
- 主要针对React组件，而非原始HTML

**集成难度**：中等

**示例代码**：
```typescript
import React from 'react';
import { Editor, Frame, Element, useNode } from '@craftjs/core';

// 可编辑文本组件
const Text = ({ text }) => {
  const { connectors: { connect, drag }, selected, actions } = useNode((state) => ({
    selected: state.events.selected,
  }));

  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{ padding: '10px', margin: '5px 0', borderRadius: '5px', border: selected ? '1px solid blue' : '1px solid transparent' }}
      contentEditable
      onBlur={(e) => actions.setProp((props) => props.text = e.target.innerText)}
      suppressContentEditableWarning
    >
      {text}
    </div>
  );
};

// 容器组件
const Container = ({ children }) => {
  const { connectors: { connect, drag } } = useNode();
  
  return (
    <div
      ref={(ref) => connect(drag(ref))}
      style={{ padding: '10px', margin: '5px 0', backgroundColor: 'rgba(0,0,0,0.05)', borderRadius: '5px' }}
    >
      {children}
    </div>
  );
};

// 注册组件
Text.craft = {
  displayName: 'Text',
  props: {
    text: 'Edit this text',
  },
};

Container.craft = {
  displayName: 'Container',
  rules: {
    canDrag: () => true,
  },
};

// 主编辑器组件
const CraftJsEditor = ({ content, onChange }) => {
  // 这里需要将HTML内容转换为Craft.js组件结构
  // 简化示例，实际实现需要HTML解析器
  
  return (
    <div style={{ height: '100%' }}>
      <Editor
        resolver={{ Text, Container }}
        onNodesChange={query => {
          const json = query.serialize();
          // 将Craft.js序列化结果转换回HTML
          // 然后调用onChange
        }}
      >
        <Frame>
          <Element is={Container} canvas>
            <Text text="Edit this text" />
            <Element is={Container} canvas>
              <Text text="Nested text" />
            </Element>
          </Element>
        </Frame>
      </Editor>
    </div>
  );
};

export default CraftJsEditor;
```

**参考资料**：
- [Craft.js官网](https://craft.js.org/)
- [GitHub仓库](https://github.com/prevwong/craft.js)

### 3.5 Quill.js + Interact.js

**概述**：结合Quill.js（富文本编辑器）和Interact.js（拖拽、缩放库）实现编辑功能。

**优势**：
- Quill提供强大的文本编辑功能
- Interact.js提供轻量级但功能强大的拖拽能力
- 两者都是轻量级库，性能良好
- 可以精确控制编辑行为

**劣势**：
- 需要手动集成两个库
- 需要额外代码处理两者之间的交互
- HTML解析和生成需要额外处理

**集成难度**：中等

**示例代码**：
```typescript
import React, { useRef, useEffect } from 'react';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import interact from 'interactjs';

const QuillInteractEditor = ({ content, onChange }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const quillInstances = useRef<Map<string, Quill>>(new Map());
  
  useEffect(() => {
    if (!containerRef.current) return;
    
    // 清空容器
    containerRef.current.innerHTML = '';
    
    // 创建一个临时容器来解析HTML
    const tempContainer = document.createElement('div');
    tempContainer.innerHTML = content;
    
    // 处理文本节点 - 使用Quill
    const textElements = tempContainer.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div');
    textElements.forEach((el, index) => {
      const id = `text-${index}`;
      const textContainer = document.createElement('div');
      textContainer.id = id;
      textContainer.className = 'editable-text';
      textContainer.innerHTML = el.innerHTML;
      containerRef.current?.appendChild(textContainer);
      
      // 初始化Quill
      const quill = new Quill(`#${id}`, {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline'],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'align': [] }],
          ]
        },
        theme: 'snow'
      });
      
      quillInstances.current.set(id, quill);
      
      quill.on('text-change', () => {
        // 更新内容
        updateContent();
      });
    });
    
    // 使所有元素可拖拽
    interact('.editable-text').draggable({
      inertia: true,
      modifiers: [
        interact.modifiers.restrictRect({
          restriction: 'parent',
          endOnly: true
        })
      ],
      autoScroll: true,
      listeners: {
        move: dragMoveListener,
        end: () => {
          updateContent();
        }
      }
    });
    
    function dragMoveListener(event) {
      const target = event.target;
      const x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx;
      const y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy;
      
      target.style.transform = `translate(${x}px, ${y}px)`;
      target.setAttribute('data-x', x);
      target.setAttribute('data-y', y);
    }
    
    // 更新内容函数
    function updateContent() {
      // 收集所有编辑后的内容
      const updatedContent = Array.from(containerRef.current?.children || [])
        .map(child => {
          const id = child.id;
          const quill = quillInstances.current.get(id);
          const content = quill ? quill.root.innerHTML : child.innerHTML;
          const x = child.getAttribute('data-x') || '0';
          const y = child.getAttribute('data-y') || '0';
          
          return `<div style="transform: translate(${x}px, ${y}px);">${content}</div>`;
        })
        .join('');
      
      onChange(updatedContent);
    }
    
    return () => {
      // 清理Quill实例
      quillInstances.current.forEach(quill => {
        // Quill没有明确的销毁方法，但可以移除DOM元素
      });
      quillInstances.current.clear();
      
      // 清理interact
      interact('.editable-text').unset();
    };
  }, [content, onChange]);
  
  return (
    <div ref={containerRef} style={{ width: '100%', height: '100%', position: 'relative' }} />
  );
};

export default QuillInteractEditor;
```

## 4. 技术方案比较

| 技术方案 | 功能完整性 | 易用性 | 性能 | 集成难度 | 维护成本 | 社区支持 |
|---------|----------|------|-----|--------|--------|--------|
| GrapesJS | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| ContentEditable + ResizeObserver | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐ |
| React DnD + Draft.js | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| Craft.js | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| Quill.js + Interact.js | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

## 5. 推荐方案

基于对项目需求和现有架构的分析，我们推荐以下两个方案：

### 5.1 首选方案：GrapesJS

GrapesJS是一个成熟的开源Web Builder框架，提供了完整的拖放界面和丰富的编辑功能。它专为创建HTML模板编辑器而设计，非常适合我们的需求。

**实施步骤**：
1. 安装GrapesJS：`npm install grapesjs grapesjs-preset-webpage`
2. 创建一个新的组件`EditableHtmlPreview.tsx`，封装GrapesJS功能
3. 在现有的`HtmlPreview.tsx`中添加编辑模式切换
4. 实现编辑内容的保存和更新机制

**注意事项**：
- GrapesJS体积较大，可能需要优化加载性能
- 需要自定义GrapesJS的UI以匹配项目风格
- 可能需要开发自定义插件以满足特定需求

### 5.2 替代方案：ContentEditable + ResizeObserver

如果需要更轻量级的解决方案，可以考虑使用浏览器原生的contentEditable属性结合ResizeObserver和拖拽事件实现。这种方案更加灵活，但需要更多的自定义代码。

**实施步骤**：
1. 扩展现有的`HtmlPreview.tsx`组件，添加编辑模式
2. 实现contentEditable功能，使文本可编辑
3. 使用拖拽事件实现元素位置调整
4. 添加编辑工具栏和保存功能

**注意事项**：
- 需要处理跨浏览器兼容性问题
- 复杂HTML结构的编辑可能需要额外处理
- 需要实现撤销/重做功能

## 6. 结论

HTML预览交互式编辑是一个复杂但可行的功能增强。通过引入成熟的编辑框架如GrapesJS，或者构建自定义解决方案，我们可以为用户提供直观的编辑体验。

建议先实现一个概念验证原型，测试用户体验和性能，然后再进行完整实现。同时，应该考虑编辑历史记录、撤销/重做功能和协作编辑等高级功能，以提供更完整的编辑体验。

## 7. 参考资料

- [GrapesJS官方文档](https://grapesjs.com/docs/)
- [MDN: contentEditable](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/contenteditable)
- [MDN: ResizeObserver](https://developer.mozilla.org/en-US/docs/Web/API/ResizeObserver)
- [React DnD文档](https://react-dnd.github.io/react-dnd/about)
- [Draft.js文档](https://draftjs.org/)
- [Craft.js文档](https://craft.js.org/docs/concepts/user-components)
- [Quill.js文档](https://quilljs.com/docs/quickstart/)
- [Interact.js文档](https://interactjs.io/docs/)
