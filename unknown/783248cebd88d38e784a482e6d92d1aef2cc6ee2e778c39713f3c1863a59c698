# PR#2 文件管理与预览区流式渲染改造——详细设计

## 一、背景与目标

为提升主页面文件管理与预览区的用户体验，Easy Coder 需将原有“整体刷新”模式升级为“流式输出”模式，实现内容的实时分段追加、无缝切换与异常降级。目标是在不破坏核心业务逻辑的前提下，系统性适配所有相关功能，确保平台稳定性、可用性与可回退性。

---

## 二、核心改造点

1. **流式内容分段追加能力**
   - 支持流式事件驱动内容分段追加（append），保证内容无丢失、无错乱。
   - 兼容整体刷新与流式模式切换，支持异常降级。

2. **状态与异常管理**
   - Tab/版本/编辑模式切换时，自动重置流式 buffer，关闭流式连接，防止内容交叉污染。
   - 编辑模式下禁用流式追加，退出编辑后恢复流式监听。
   - 提供“回退到整体刷新”入口，异常时自动降级。

3. **UI与交互适配**
   - 预览区、代码区、编辑区均需响应流式内容变化，保证 UI 实时更新。
   - 关键流程增加日志与监控，便于问题定位。

---

## 三、组件结构与数据流

### 1. ContentViewer（核心改造点）

- 新增 `isStreaming` 状态与 `appendContent(chunk: string)` 方法。
- 支持流式事件驱动内容分段追加，自动触发 UI 更新。
- 切换 Tab/版本/编辑模式时，自动重置流式 buffer，关闭流式连接。
- 提供“流式/整体刷新”切换入口，异常时自动降级。

**关键状态与方法设计示例：**
```typescript
const [isStreaming, setIsStreaming] = useState(false);
const [streamBuffer, setStreamBuffer] = useState('');
const appendContent = (chunk: string) => {
  setStreamBuffer(prev => prev + chunk);
  setContent(prev => prev + chunk);
};
const resetStream = () => {
  setIsStreaming(false);
  setStreamBuffer('');
  // 关闭流式连接
};
```

### 2. EditableHtmlPreview / CodeDisplay / MarkdownPreview

- 仅依赖 `content` props，自动响应内容变化。
- 编辑模式下禁用流式追加，退出编辑后恢复流式监听。

---

## 四、交互与异常处理

- 编辑模式下自动暂停流式监听，防止内容冲突。
- 切换 Tab/版本时自动重置流式状态，防止内容错乱。
- 提供“回退到整体刷新”入口，异常时自动降级。
- 关键流程增加日志与监控，便于问题定位。

---

## 五、验收标准

- 流式 append 无丢失/错乱，切换流畅，兼容大文件。
- 切回整体刷新模式无副作用。
- 编辑、Tab/版本切换、异常降级等场景下内容一致性与 UI 响应正确。

---

## 六、影响面与变更文件

- `components/content-viewer/content-viewer.tsx`：核心流式能力改造
- 相关下游组件（如 `editable-html-preview.tsx`、`code-display.tsx`）：仅需保证 props 响应式
- 可能涉及流式事件分发/管理的 hooks 或 lib 层

---

## 七、开发建议

- 以 ContentViewer 为唯一流式内容注入点，向下游组件传递最新内容。
- 设计流式 buffer 与整体内容快照机制，支持回退与降级。
- 增加必要的单元测试与集成测试，完善异常降级与切换逻辑。

---

## 八、附录

- 参考《流式输出升级-全局影响与任务拆解方案.md》PR#2相关章节
- 相关代码实现需遵循 TypeScript、React 19、Next.js 15、Tailwind CSS 及本项目代码规范