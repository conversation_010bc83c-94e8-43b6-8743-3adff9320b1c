# PR#3 对话区进度与摘要联动——详细设计与AI协议兼容性方案

## 一、背景与目标

本PR目标：在主页面对话区（ConversationPanel）实现流式输出下的实时进度与摘要展示，确保流式与非流式状态同步，提升用户体验。需兼容多AI Provider底层协议，保证多模型场景下的功能一致性与降级安全。

---

## 二、核心需求与影响面

- **实时进度**：对话区根据流式事件动态展示生成进度（如token数、百分比、动画等）。
- **实时摘要**：流式生成过程中，阶段性展示摘要内容（如小结、重点提示）。
- **状态同步**：流式/非流式、Tab/任务切换时，进度与摘要展示需保持一致，切换无延迟、无错乱。
- **回退机制**：流式异常或降级时，仅展示最终文本，进度/摘要不联动。

**影响面**：任务执行、状态管理、UI交互、AI协议适配。

---

## 三、详细任务拆解

| 子任务 | 说明 | 验收标准 | 回退策略 |
|--------|------|----------|----------|
| 1. 流式事件协议扩展 | SSE/流式事件支持progress/summary | 事件分发准确，兼容原有协议 | 异常时忽略新事件 |
| 2. 状态管理适配 | store/context支持进度与摘要 | 状态同步无延迟、无错乱 | 异常时重置状态 |
| 3. UI组件改造 | ConversationPanel支持进度/摘要展示 | 进度/摘要准确、UI响应流畅 | 隐藏进度/摘要 |
| 4. 场景联动与异常处理 | 切换/异常时状态清理与降级 | 切换无副作用，异常可恢复 | 仅展示文本 |

---

## 四、AI调用底层协议兼容性说明

### 1. 多Provider流式协议标准化

- 支持OpenAI、Anthropic、DeepSeek、XAI等主流大模型Provider，均通过各自SDK或API实现流式输出（SSE/ReadableStream）。
- 各Provider流式响应格式存在差异，平台已在 [`lib/providers/openai.ts`](lib/providers/openai.ts)、[`lib/providers/anthropic.ts`](lib/providers/anthropic.ts)、[`lib/providers/deepseek.ts`](lib/providers/deepseek.ts)、[`lib/providers/xai.ts`](lib/providers/xai.ts) 等文件中实现 `streamingChatCompletion` 方法，统一分发标准事件对象（content/file/error/finish/progress）。
- [`lib/streaming/stream-parser.ts`](lib/streaming/stream-parser.ts) 和 [`lib/streaming/stream-event-types.ts`](lib/streaming/stream-event-types.ts) 定义了平台内部流式事件协议，支持 `progress`、`summary` 等扩展事件类型。

### 2. 进度与摘要事件的兼容处理

- 各Provider原生流式协议大多仅支持内容片段（content），部分如Anthropic支持阶段性summary，OpenAI/DeepSeek/XAI需通过平台层自定义进度/摘要事件。
- 平台在Provider适配层（如`onProgress`/`onSummary`回调）对原始流进行解析和补充，生成标准化的`progress`和`summary`事件，供上层（如ConversationPanel）消费。
- 事件分发流程：Provider流 → 标准事件对象 → [`lib/streaming/stream-parser.ts`](lib/streaming/stream-parser.ts) → 全局状态 → UI组件。

### 3. 兼容性与降级策略

- 若Provider不支持进度/摘要事件，平台自动降级为仅内容流式展示，进度/摘要UI自动隐藏，保证体验一致性。
- 所有事件分发均有类型安全兜底，未识别事件自动归为content或error，防止协议变更导致前端崩溃。
- 未来如有新Provider接入，仅需实现`streamingChatCompletion`标准事件分发接口，即可无缝兼容。

### 4. 相关核心文件

- [`lib/providers/openai.ts`](lib/providers/openai.ts)
- [`lib/providers/anthropic.ts`](lib/providers/anthropic.ts)
- [`lib/providers/deepseek.ts`](lib/providers/deepseek.ts)
- [`lib/providers/xai.ts`](lib/providers/xai.ts)
- [`lib/streaming/stream-parser.ts`](lib/streaming/stream-parser.ts)
- [`lib/streaming/stream-event-types.ts`](lib/streaming/stream-event-types.ts)
- [`lib/chat/index.ts`](lib/chat/index.ts)

---

## 五、系统流程与架构关系

```mermaid
flowchart TD
    A[流式事件分发] --> B[全局状态管理]
    B --> C[ConversationPanel]
    C --> D[进度展示]
    C --> E[摘要展示]
    F[切换/异常] --> B
    F --> C
```

---

## 六、风险与注意事项

- 流式事件丢失/延迟导致进度与摘要不同步
- UI切换（如Tab/任务切换）时状态未及时清理
- 兼容性：需保证非流式模式下无副作用
- 性能：大对话流下进度/摘要频繁刷新可能影响渲染

---

## 七、交付物与变更点清单

- 本设计文档
- 受影响文件列表与改动说明
- 详细实现规范（供Code模式开发用）

---

## 八、受影响文件（初步推测，需实际确认）

- `components/conversation-panel/conversation-panel.tsx`
- `lib/streaming/stream-parser.ts`
- `lib/streaming/stream-event-types.ts`
- `lib/ai-store.ts` 或全局store/context相关
- 进度/摘要子组件（如无则需新建）
- 各AI Provider适配文件

---

## 九、结论

本方案已系统梳理对话区进度与摘要联动的全流程设计，补充了AI调用底层协议的多Provider兼容性与降级机制，确保平台在多模型流式场景下的功能一致性与可维护性。可直接作为开发与评审依据。