"use client";

import { useState, useEffect, useMemo } from "react";
import { useCookies } from "next-client-cookies";
import { AIConfigManager } from "@/lib/ai-config";
import { ProviderType } from "@/lib/provider-factory";

export default function TestAPIPage() {
  const cookies = useCookies();
  const configManager = useMemo(() => new AIConfigManager(cookies), [cookies]);
  
  const [apiKey, setApiKey] = useState("");
  const [baseUrl, setBaseUrl] = useState("");
  const [provider, setProvider] = useState<ProviderType>("openai");
  const [model, setModel] = useState("gpt-3.5-turbo");
  const [prompt, setPrompt] = useState("Hello, how are you today?");
  const [response, setResponse] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [testStatus, setTestStatus] = useState<"idle" | "success" | "error">("idle");
  
  // 加载保存的配置
  useEffect(() => {
    const loadConfig = async () => {
      const config = await configManager.getConfig();
      if (config.provider === "openai") {
        setApiKey(config.apiKey || "");
        setBaseUrl(config.baseUrl || "");
        setProvider("openai");
      }
    };
    
    loadConfig();
  }, [configManager]);
  
  // 保存配置
  const saveConfig = async () => {
    await configManager.saveConfig({
      provider,
      model,
      apiKey,
      baseUrl
    });
    
    alert("配置已保存");
  };
  
  // 测试API调用
  const testApiCall = async () => {
    setLoading(true);
    setResponse("");
    setError("");
    setTestStatus("idle");
    
    try {
      // 保存当前配置
      await configManager.saveConfig({
        provider,
        model,
        apiKey,
        baseUrl
      });
      
      // 构建请求
      const response = await fetch("/api/test-openai", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          model,
          provider
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "API调用失败");
      }
      
      const data = await response.json();
      setResponse(data.response);
      setTestStatus("success");
    } catch (err) {
      console.error("API调用错误:", err);
      setError(err instanceof Error ? err.message : String(err));
      setTestStatus("error");
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <>
      <h1 className="text-2xl font-bold mb-6">OpenAI API集成 - API调用测试</h1>
      
      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-4">API配置</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              提供商
            </label>
            <select
              value={provider}
              onChange={(e) => setProvider(e.target.value as ProviderType)}
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            >
              <option value="openai">OpenAI</option>
              <option value="together">OpenAI Compatible</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              模型
            </label>
            <select
              value={model}
              onChange={(e) => setModel(e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            >
              <option value="gpt-4o">GPT-4o</option>
              <option value="gpt-4-turbo">GPT-4 Turbo</option>
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              API密钥
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="输入OpenAI API密钥"
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              基础URL (可选)
            </label>
            <input
              type="text"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              placeholder="输入自定义API地址"
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            />
          </div>
          
          <button
            onClick={saveConfig}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            保存配置
          </button>
        </div>
      </div>
      
      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-4">API调用测试</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              测试提示词
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              rows={3}
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            />
          </div>
          
          <button
            onClick={testApiCall}
            disabled={loading}
            className={`px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 ${
              loading ? "opacity-50 cursor-not-allowed" : ""
            }`}
          >
            {loading ? "测试中..." : "测试API调用"}
          </button>
        </div>
      </div>
      
      {testStatus !== "idle" && (
        <div className={`p-6 rounded-lg mb-6 ${
          testStatus === "success" ? "bg-green-50" : "bg-red-50"
        }`}>
          <h2 className="text-lg font-semibold mb-4">
            {testStatus === "success" ? "测试成功 ✅" : "测试失败 ❌"}
          </h2>
          
          {testStatus === "success" ? (
            <div>
              <h3 className="font-medium mb-2">API响应:</h3>
              <div className="bg-white p-4 rounded border border-gray-200">
                <pre className="whitespace-pre-wrap">{response}</pre>
              </div>
            </div>
          ) : (
            <div>
              <h3 className="font-medium mb-2">错误信息:</h3>
              <div className="bg-white p-4 rounded border border-red-200 text-red-600">
                {error}
              </div>
            </div>
          )}
        </div>
      )}
      
      <div className="mt-8">
        <a 
          href="/test-openai"
          className="inline-block px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          返回测试首页
        </a>
      </div>
    </>
  );
}
