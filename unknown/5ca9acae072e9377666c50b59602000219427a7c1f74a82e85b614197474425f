"use client";

import React from 'react';
import { ToolbarProps } from './types';

const Toolbar: React.FC<ToolbarProps> = ({
  contentType,
  viewMode,
  isEditing = false,
  canEdit = false,
  onContentTypeChange,
  onViewModeChange,
  onEditToggle,
}) => {
  return (
    <div className="flex justify-between items-center p-2 bg-gray-50 border-b border-gray-200">
      {/* 内容类型切换 */}
      <div className="flex space-x-2">
        <button 
          className={`px-4 py-1.5 rounded-md text-sm font-medium transition-colors ${
            contentType === 'html' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => onContentTypeChange('html')}
        >
          HTML
        </button>
        <button 
          className={`px-4 py-1.5 rounded-md text-sm font-medium transition-colors ${
            contentType === 'markdown' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => onContentTypeChange('markdown')}
        >
          Markdown
        </button>
      </div>
      
      {/* 视图模式切换 */}
      <div className="flex space-x-2">
        <button 
          className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
            viewMode === 'code' 
              ? 'bg-gray-200 text-gray-800' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => onViewModeChange('code')}
        >
          代码
        </button>
        <button 
          className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
            viewMode === 'preview' 
              ? 'bg-gray-200 text-gray-800' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => onViewModeChange('preview')}
        >
          预览
        </button>
        <button 
          className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
            viewMode === 'split' 
              ? 'bg-gray-200 text-gray-800' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
          onClick={() => onViewModeChange('split')}
        >
          分屏
        </button>
        
        {/* 编辑/保存按钮 - 只在可编辑且内容类型为HTML时显示 */}
        {canEdit && contentType === 'html' && (
          <button 
            className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
              isEditing
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
            onClick={onEditToggle}
          >
            {isEditing ? '保存' : '编辑'}
          </button>
        )}
      </div>
    </div>
  );
};

export default Toolbar;
