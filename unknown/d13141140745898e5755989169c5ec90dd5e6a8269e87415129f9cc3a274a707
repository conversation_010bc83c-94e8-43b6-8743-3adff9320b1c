import { getPrisma } from "@/lib/prisma";
import { z } from "zod";
import { ProviderFactory, ProviderType, createChatCompletion } from "@/lib/provider-factory";
import { cookies } from "next/headers";

interface StreamChunk {
  choices?: Array<{
    delta?: {
      content?: string;
    };
  }>;
  [key: string]: any;
}

export async function POST(req: Request) {
  try {
    const prisma = getPrisma();
    const { messageId, model, provider } = await req.json();
    
    console.log("收到完成流请求", { messageId, model, provider });

    // 获取消息
    const message = await prisma.message.findUnique({
      where: { id: messageId },
    });

    if (!message) {
      console.error("消息未找到", { messageId });
      return new Response(
        JSON.stringify({ error: "消息未找到" }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      );
    }
    
    console.log("找到消息", { messageId, chatId: message.chatId });

    // 获取对话历史
    const messagesRes = await prisma.message.findMany({
      where: { chatId: message.chatId, position: { lte: message.position } },
      orderBy: { position: "asc" },
    });
    
    console.log("获取到对话历史", { count: messagesRes.length });

    let messages;
    try {
      messages = z
        .array(
          z.object({
            role: z.enum(["system", "user", "assistant"]),
            content: z.string(),
          }),
        )
        .parse(messagesRes);
      
      if (messages.length > 10) {
        messages = [messages[0], messages[1], messages[2], ...messages.slice(-7)];
      }
      
      console.log("处理后的消息数量", { count: messages.length });
    } catch (error) {
      console.error("消息格式验证失败", error);
      return new Response(
        JSON.stringify({ error: "消息格式验证失败" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // 获取配置
    const cookieStore = await cookies();
    const apiKeyKey = `ai_api_key_${provider}`;
    const baseUrlKey = `ai_base_url_${provider}`;
    
    const apiKey = cookieStore.get(apiKeyKey)?.value;
    const baseUrl = cookieStore.get(baseUrlKey)?.value;
    
    console.log("从cookies直接获取的配置信息", { 
      provider, 
      hasApiKey: !!apiKey, 
      apiKeyType: typeof apiKey,
      hasBaseUrl: !!baseUrl 
    });
    
    // 验证API密钥
    if (provider === "openai" && !apiKey) {
      console.error("使用OpenAI API需要提供有效的API密钥");
      return new Response(
        JSON.stringify({ error: "使用OpenAI API需要提供有效的API密钥" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
    
    console.log("准备调用API", { provider, model });

    try {
      // 调用API
      const stream = await createChatCompletion(
        provider as ProviderType,
        {
          apiKey,
          baseUrl
        },
        {
          model,
          messages: messages.map((m) => ({ role: m.role, content: m.content })),
          temperature: 0.2,
          max_tokens: 8192,
          sessionId: message.chatId,
          sessionName: "Chat Session",
          stream: true // 确保启用流式输出
        }
      );

      console.log("API调用成功，检查流类型:", {
        isStream: stream && typeof stream === 'object',
        hasIterator: stream && typeof stream[Symbol.asyncIterator] === 'function',
        hasOn: stream && typeof stream.on === 'function',
        hasGetReader: stream && typeof stream.getReader === 'function'
      });

      // 创建标准化的ReadableStream
      const encoder = new TextEncoder();
      const readableStream = new ReadableStream({
        async start(controller) {
          try {
            console.log("开始处理流式响应");
            
            if (stream && typeof stream.on === 'function') {
              // 事件型流
              stream.on('data', (chunk: StreamChunk) => {
                const content = chunk.choices?.[0]?.delta?.content || "";
                if (content) {
                  controller.enqueue(encoder.encode(content));
                }
              });
              
              stream.on('end', () => {
                console.log("流式响应处理完成");
                controller.close();
              });
              
              stream.on('error', (error: Error) => {
                console.error("流处理错误:", error);
                controller.error(error);
              });
            } else if (stream && typeof stream[Symbol.asyncIterator] === 'function') {
              // 异步迭代器型流
              try {
                for await (const chunk of stream as AsyncIterable<StreamChunk>) {
                  const content = chunk.choices?.[0]?.delta?.content || "";
                  if (content) {
                    controller.enqueue(encoder.encode(content));
                  }
                }
                console.log("流式响应处理完成");
                controller.close();
              } catch (error) {
                console.error("异步迭代器处理错误:", error);
                controller.error(error);
              }
            } else if (stream && typeof stream.getReader === 'function') {
              // ReadableStream类型
              try {
                const reader = stream.getReader();
                while (true) {
                  const { done, value } = await reader.read();
                  if (done) break;
                  
                  let content = "";
                  if (value instanceof Uint8Array) {
                    content = new TextDecoder().decode(value);
                  } else if (typeof value === 'string') {
                    content = value;
                  } else if (value && typeof value === 'object') {
                    content = value.choices?.[0]?.delta?.content || "";
                  }
                  
                  if (content) {
                    controller.enqueue(encoder.encode(content));
                  }
                }
                console.log("流式响应处理完成");
                controller.close();
              } catch (error) {
                console.error("ReadableStream处理错误:", error);
                controller.error(error);
              }
            } else {
              throw new Error("不支持的流类型");
            }
          } catch (error) {
            console.error("流处理错误:", error);
            controller.error(error);
          }
        },
        cancel() {
          console.log("流被取消");
          if (stream && typeof stream.destroy === 'function') {
            stream.destroy();
          }
        }
      });

      // 返回标准化流
      return new Response(readableStream, {
        headers: {
          "Content-Type": "text/event-stream",
          "Cache-Control": "no-cache",
          "Connection": "keep-alive",
          "Transfer-Encoding": "chunked"
        },
      });
    } catch (error) {
      console.error("API调用失败", error);
      return new Response(
        JSON.stringify({ error: error instanceof Error ? error.message : "API调用失败" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      );
    }
  } catch (error) {
    console.error("请求处理失败", error);
    return new Response(
      JSON.stringify({ error: error instanceof Error ? error.message : "请求处理失败" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}

export const maxDuration = 45;
