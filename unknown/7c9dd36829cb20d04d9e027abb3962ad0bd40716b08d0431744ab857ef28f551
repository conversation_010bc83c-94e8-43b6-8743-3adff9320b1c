1、当前用户发送消息后，模型的回复框架需要修改。希望达到的效果如下：
- 根据用户发送的信息进行任务规划和拆分，拆分后的每一个任务从1开始编号。
- 确保任务的前后依赖关系，任务必须严格按照编号的顺序依次执行。
- 规划的任务列表内容需要输出到Todo.md的文档中显示，采用Markdown格式输出。
- 规划完成后，需要严格按照顺序，依次执行任务，每一个单独的任务都是一个任务调用的模型输出。
- 所有规划任务完成后，做出总结，并输出到summary.md文件中。然后停止模型调用。
- 每次调用的UI呈现部分，复用现有项目的多文件解析，以及对话页面的内容展示。

说明：
1、任务规划工作流第一步需要做的事情
2、每个任务应该作为对模型的单独API调用来执行
3、Todo.md和summary.md文件应该与其他生成的文件一起显示在右侧面板中
4、对话页面能显示显示每个任务的状态（例如，待处理、进行中、已完成）
5、用户不能够与任务列表交互
6、任务列表和摘要需要在会话之间保持
请你按照这个需求，先跟我进行对话，确保没有模糊的点，然后再进行技术方案的制定。


2、请你按照这个需求，先跟我进行对话，确保没有模糊的点，然后再进行技术方案的制定。

3、首先实现一个单文件的验证版本，通过简单的任务测试，跑通核心机制。

4、测试任务：请生成一个简单的网页，包含标题和一段描述文字。


UI交互机制调整
1、不同的任务更新相同的文件，预览页面需要覆盖之前的版本进行显示，而不是新增一个组件进行显示。
    - 多个任务生成或修改同一个文件名的文件时，系统应该更新现有文件的内容，而不是创建一个新的文件组件。
    - 系统应该如何识别两个文件是"相同的"，仅通过文件名和文件类型来判断，需要注意在规划阶段对文件命名的统一规划。
    - 当文件被更新时，自动刷新显示最新内容，如果能做版本控制最好。
    - 最好需要保留文件的历史版本，以便用户可以查看文件的变更历史
    - 最好在对话页面显示文件状态，比如创建文件xxx，正在写入文件xxx，文件xxx写入完成。

5、问题仍然存在，先搜集足够的信息，确认原因后，再制定修复方案。

