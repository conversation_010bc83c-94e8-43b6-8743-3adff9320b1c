# 通用任务规划与执行系统集成指南

本指南提供了将通用任务规划与执行系统集成到现有代码中的步骤。

## 文件概述

我们创建了以下新文件：

1. `extractTasksFromResponse-new.ts` - 增强的任务提取机制
2. `executeTask-new.ts` - 改进的任务执行函数
3. `taskExecutionFlow-new.ts` - 增强的任务执行流程
4. `generateSummary-new.ts` - 增强的总结生成功能

此外，我们已经更新了以下文件：

1. `app\api\chat\route.ts` - 更新了系统提示词
2. `app\content-generator\types.ts` - 更新了Task类型定义

## 集成步骤

### 1. 更新任务提取机制

在 `app\content-generator\content-generator-client.tsx` 文件中，找到 `extractTasksFromResponse` 函数，并用 `extractTasksFromResponse-new.ts` 中的代码替换它。

### 2. 更新任务执行函数

在 `app\content-generator\task-functions.ts` 文件中，找到 `executeTask` 函数，并用 `executeTask-new.ts` 中的代码替换它。同时，添加 `normalizeFileName` 函数到文件末尾。

### 3. 更新任务执行流程

在 `app\content-generator\content-generator-client.tsx` 文件中，找到任务执行的 `useEffect` 钩子（大约在第956行），并用 `taskExecutionFlow-new.ts` 中的代码替换它。

### 4. 更新总结生成功能

在 `app\content-generator\task-functions.ts` 文件中，找到 `generateSummary` 函数，并用 `generateSummary-new.ts` 中的代码替换它。

## 注意事项

1. 确保在替换代码时保持正确的导入和导出语句。
2. 检查类型定义是否正确，特别是在使用新增的 `dependencies` 和 `outputs` 字段时。
3. 测试系统在不同场景下的行为，特别是任务依赖关系的处理。
4. 如果遇到问题，可以逐步集成这些更改，先从系统提示词和类型定义开始。

## 测试建议

1. 创建一个简单的任务，验证任务提取机制是否正常工作。
2. 创建具有依赖关系的多个任务，验证任务执行流程是否正确处理依赖。
3. 测试文件命名和版本管理功能，确保同名文件能够正确更新而不是创建新文件。
4. 测试任务失败和重试机制，确保系统能够适当处理错误情况。

## 未来改进方向

1. 添加更多文件类型的支持，如JavaScript、CSS等。
2. 增强依赖分析能力，支持更复杂的依赖关系表达。
3. 改进任务执行状态的可视化，提供更详细的进度信息。
4. 添加用户干预机制，允许用户在任务执行过程中进行调整。
