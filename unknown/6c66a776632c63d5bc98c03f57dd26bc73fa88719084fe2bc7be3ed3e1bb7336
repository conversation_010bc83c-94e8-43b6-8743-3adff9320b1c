'use client';

import React from 'react';
import dynamic from 'next/dynamic';

// 动态导入ContentViewer组件，避免SSR水合错误
const ContentViewer = dynamic(
  () => import('./content-viewer').then(mod => ({ default: mod.ContentViewer })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-full bg-gray-50">
        <div className="text-center">
          <p className="text-gray-600 mb-4">加载内容查看器...</p>
        </div>
      </div>
    )
  }
);

interface ContentViewerWrapperProps {
  content: string;
  contentType: 'html' | 'markdown';
  initialViewMode?: 'code' | 'preview' | 'split';
  onViewModeChange?: (viewMode: 'code' | 'preview' | 'split') => void;
  onContentChange?: (newContent: string) => void;
  editable?: boolean;
  splitRatio?: number; // 添加分屏比例属性
}

export default function ContentViewerWrapper({
  content,
  contentType,
  initialViewMode = 'code',
  onViewModeChange,
  onContentChange,
  editable = true,
  splitRatio = 50
}: ContentViewerWrapperProps) {
  // 记录内容长度和预览
  console.log(`ContentViewerWrapper: content type=${contentType}, length=${content.length}`);
  console.log(`ContentViewerWrapper: content preview=${content.substring(0, 100)}...`);

  // 处理内容变更
  const handleContentChange = (newContent: string) => {
    console.log(`ContentViewerWrapper: content changed, new length=${newContent.length}`);
    if (onContentChange) {
      onContentChange(newContent);
    }
  };

  return (
    <ContentViewer
      content={content}
      contentType={contentType}
      initialViewMode={initialViewMode}
      onViewModeChange={onViewModeChange}
      onChange={handleContentChange}
      editable={editable && contentType === 'html'} // 目前只支持HTML编辑
      splitRatio={splitRatio}
    />
  );
}
