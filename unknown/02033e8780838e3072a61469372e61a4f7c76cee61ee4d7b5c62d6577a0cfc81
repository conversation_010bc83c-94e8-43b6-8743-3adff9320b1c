<#
.SYNOPSIS
停止开发服务器

.DESCRIPTION
查找并终止node进程

.PARAMETER Force
强制终止不提示确认

.EXAMPLE
./stop.ps1
./stop.ps1 -Force
#>
param(
    [switch]$Force
)

$processes = Get-Process node -ErrorAction SilentlyContinue

if (-not $processes) {
    Write-Host "ℹ️ 没有找到运行的node进程" -ForegroundColor Yellow
    exit 0
}

if (-not $Force) {
    $confirmation = Read-Host "⚠️ 确认要终止 $($processes.Count) 个node进程? [y/n]"
    if ($confirmation -ne 'y') {
        Write-Host "操作已取消" -ForegroundColor Yellow
        exit 0
    }
}

Write-Host "🛑 正在停止node进程..."
$processes | Stop-Process -Force

if ($?) {
    Write-Host "✅ 成功停止所有node进程" -ForegroundColor Green
} else {
    Write-Host "❌ 停止进程时出错" -ForegroundColor Red
    exit 1
}