"use client";

import { useState, useEffect, useMemo } from "react";
import { useCookies } from "next-client-cookies";
import { AIConfigManager } from "@/lib/ai-config";

export default function TestPage() {
  const cookies = useCookies();
  const configManager = useMemo(() => new AIConfigManager(cookies), [cookies]);
  
  const [apiKey, setApiKey] = useState("");
  const [baseUrl, setBaseUrl] = useState("");
  const [testResults, setTestResults] = useState<{
    saveApiKey: boolean | null;
    loadApiKey: boolean | null;
    saveBaseUrl: boolean | null;
    loadBaseUrl: boolean | null;
  }>({
    saveApiKey: null,
    loadApiKey: null,
    saveBaseUrl: null,
    loadBaseUrl: null,
  });
  
  // 运行测试
  const runTests = async () => {
    // 测试保存API Key
    const testSaveApiKey = () => {
      try {
        configManager.saveApiKey("openai", "test-api-key");
        return true;
      } catch (error) {
        console.error("保存API Key失败:", error);
        return false;
      }
    };
    
    // 测试加载API Key
    const testLoadApiKey = async () => {
      try {
        const savedApiKey = await configManager.getApiKey("openai");
        return savedApiKey === "test-api-key";
      } catch (error) {
        console.error("加载API Key失败:", error);
        return false;
      }
    };
    
    // 测试保存Base URL
    const testSaveBaseUrl = () => {
      try {
        configManager.saveBaseUrl("openai", "https://test-base-url.com");
        return true;
      } catch (error) {
        console.error("保存Base URL失败:", error);
        return false;
      }
    };
    
    // 测试加载Base URL
    const testLoadBaseUrl = async () => {
      try {
        const savedBaseUrl = await configManager.getBaseUrl("openai");
        return savedBaseUrl === "https://test-base-url.com";
      } catch (error) {
        console.error("加载Base URL失败:", error);
        return false;
      }
    };
    
    // 运行所有测试
    const results = {
      saveApiKey: testSaveApiKey(),
      loadApiKey: await testLoadApiKey(),
      saveBaseUrl: testSaveBaseUrl(),
      loadBaseUrl: await testLoadBaseUrl(),
    };
    
    setTestResults(results);
    
    // 恢复原始值
    const originalApiKey = await configManager.getApiKey("openai");
    const originalBaseUrl = await configManager.getBaseUrl("openai");
    setApiKey(originalApiKey || "");
    setBaseUrl(originalBaseUrl || "");
  };
  
  // 加载保存的配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const apiKey = await configManager.getApiKey("openai");
        const baseUrl = await configManager.getBaseUrl("openai");
        setApiKey(apiKey || "");
        setBaseUrl(baseUrl || "");
      } catch (error) {
        console.error("加载配置失败:", error);
        // 设置为空字符串作为fallback
        setApiKey("");
        setBaseUrl("");
      }
    };

    loadConfig();
  }, [configManager]);
  
  // 保存配置
  const saveConfig = () => {
    configManager.saveApiKey("openai", apiKey);
    configManager.saveBaseUrl("openai", baseUrl);
    
    alert("配置已保存");
  };
  
  return (
    <>
      <h1 className="text-2xl font-bold mb-6">OpenAI API集成 - 配置管理测试</h1>
      
      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-4">配置设置</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              OpenAI API密钥
            </label>
            <input
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="输入OpenAI API密钥"
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              OpenAI 基础URL
            </label>
            <input
              type="text"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              placeholder="输入OpenAI 基础URL"
              className="w-full rounded-md border border-gray-300 px-3 py-2"
            />
          </div>
          
          <button
            onClick={saveConfig}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            保存配置
          </button>
        </div>
      </div>
      
      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-4">配置管理测试</h2>
        
        <div className="space-y-4">
          <button
            onClick={runTests}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
          >
            运行测试
          </button>
          
          {Object.entries(testResults).some(([_, value]) => value !== null) && (
            <div className="mt-4">
              <h3 className="font-medium mb-2">测试结果:</h3>
              <div className="space-y-2">
                <div className="flex items-center">
                  <span className="w-32">保存API Key:</span>
                  {testResults.saveApiKey === null ? (
                    <span className="text-gray-500">未测试</span>
                  ) : testResults.saveApiKey ? (
                    <span className="text-green-500">通过 ✓</span>
                  ) : (
                    <span className="text-red-500">失败 ✗</span>
                  )}
                </div>
                
                <div className="flex items-center">
                  <span className="w-32">加载API Key:</span>
                  {testResults.loadApiKey === null ? (
                    <span className="text-gray-500">未测试</span>
                  ) : testResults.loadApiKey ? (
                    <span className="text-green-500">通过 ✓</span>
                  ) : (
                    <span className="text-red-500">失败 ✗</span>
                  )}
                </div>
                
                <div className="flex items-center">
                  <span className="w-32">保存Base URL:</span>
                  {testResults.saveBaseUrl === null ? (
                    <span className="text-gray-500">未测试</span>
                  ) : testResults.saveBaseUrl ? (
                    <span className="text-green-500">通过 ✓</span>
                  ) : (
                    <span className="text-red-500">失败 ✗</span>
                  )}
                </div>
                
                <div className="flex items-center">
                  <span className="w-32">加载Base URL:</span>
                  {testResults.loadBaseUrl === null ? (
                    <span className="text-gray-500">未测试</span>
                  ) : testResults.loadBaseUrl ? (
                    <span className="text-green-500">通过 ✓</span>
                  ) : (
                    <span className="text-red-500">失败 ✗</span>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-8">
        <a 
          href="/test-openai"
          className="inline-block px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
        >
          返回测试首页
        </a>
      </div>
    </>
  );
}









