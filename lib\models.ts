export type ModelProvider = 'openai' | 'xai' | 'deepseek' | 'anthropic';

export interface ModelConfig {
  id: string;
  name: string;
  provider: ModelProvider;
  maxTokens?: number;
  temperature?: number;
  isCustom?: boolean; // 标识是否为自定义模型
}

// 默认模型配置
const OPENAI_MODELS: ModelConfig[] = [
  { id: 'gpt-4o', name: 'GPT-4o', provider: 'openai', maxTokens: 8192, temperature: 0.7 },
  { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', provider: 'openai', maxTokens: 8192, temperature: 0.7 },
  { id: 'gpt-4', name: 'GPT-4', provider: 'openai', maxTokens: 8192, temperature: 0.7 },
  { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'openai', maxTokens: 8192, temperature: 0.7 }
];

const XAI_MODELS: ModelConfig[] = [
  { id: 'grok-3-beta', name: 'Grok-3 Beta', provider: 'xai', maxTokens: 8192, temperature: 0.7 }
];

const DEEPSEEK_MODELS: ModelConfig[] = [
  { id: 'deepseek-chat', name: 'DeepSeek V3', provider: 'deepseek', maxTokens: 8192, temperature: 0.7 }
];

const ANTHROPIC_MODELS: ModelConfig[] = [
  // { id: 'claude-3-7-sonnet-20250219', name: 'Claude 3.7 Sonnet', provider: 'anthropic', maxTokens: 32000, temperature: 0.7 },
  { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet', provider: 'anthropic', maxTokens: 8192, temperature: 0.7 },
  { id: 'claude-3-haiku', name: 'Claude 3 Haiku', provider: 'anthropic', maxTokens: 8192, temperature: 0.7 }
];

// 所有默认模型
export const ALL_MODELS: ModelConfig[] = [
  ...OPENAI_MODELS,
  ...XAI_MODELS,
  ...DEEPSEEK_MODELS,
  ...ANTHROPIC_MODELS
];

/**
 * 获取所有模型（包括自定义模型）
 */
export function getAllModels(customModels: ModelConfig[] = []): ModelConfig[] {
  return [...ALL_MODELS, ...customModels];
}

/**
 * 根据提供商获取模型（包括自定义模型）
 */
export function getModelsByProvider(provider: ModelProvider, customModels: ModelConfig[] = []): ModelConfig[] {
  let defaultModels: ModelConfig[] = [];

  switch (provider) {
    case 'openai':
      defaultModels = OPENAI_MODELS;
      break;
    case 'xai':
      defaultModels = XAI_MODELS;
      break;
    case 'deepseek':
      defaultModels = DEEPSEEK_MODELS;
      break;
    case 'anthropic':
      defaultModels = ANTHROPIC_MODELS;
      break;
    default:
      defaultModels = [];
  }

  // 添加该提供商的自定义模型
  const providerCustomModels = customModels.filter(m => m.provider === provider);

  return [...defaultModels, ...providerCustomModels];
}

/**
 * 根据ID获取模型配置（包括自定义模型）
 */
export function getModelById(id: string, customModels: ModelConfig[] = []): ModelConfig | undefined {
  // 先在默认模型中查找
  const defaultModel = ALL_MODELS.find(model => model.id === id);
  if (defaultModel) {
    return defaultModel;
  }

  // 再在自定义模型中查找
  return customModels.find(model => model.id === id);
}
