/**
 * Provider系统入口
 * 导出所有Provider相关功能
 */

import { OpenAIProvider } from './openai';
import { XAIProvider } from './xai';
import { DeepSeekProvider } from './deepseek';
import { AnthropicProvider } from './anthropic';
import { BaseProvider, ProviderConfig } from './base';
import { ModelProvider } from '../models';

// 导出类型定义
export * from './base';

/**
 * Provider工厂
 * 根据提供商类型创建对应的Provider实例
 */
export class ProviderFactory {
  /**
   * 创建Provider实例
   */
  static createProvider(type: ModelProvider, config: ProviderConfig): BaseProvider {
    switch (type) {
      case 'openai':
        return new OpenAIProvider(config);
      case 'xai':
        return new XAIProvider(config);
      case 'deepseek':
        return new DeepSeekProvider(config);
      case 'anthropic':
        return new AnthropicProvider(config);
      default:
        throw new Error(`不支持的提供商类型: ${type}`);
    }
  }
}
