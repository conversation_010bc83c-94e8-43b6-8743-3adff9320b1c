<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试幻灯片 - 定义</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            font-family: 'Montserrat', sans-serif;
            position: relative;
            padding: 64px;
            box-sizing: border-box;
        }

        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            padding: 24px;
        }

        .glow {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            opacity: 0.3;
        }

        .glow-1 {
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, #38bdf8, transparent);
            top: 10%;
            right: 10%;
        }

        .glow-2 {
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, #a78bfa, transparent);
            bottom: 20%;
            left: 15%;
        }

        h1 {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        h2 {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .definition-box {
            background: rgba(56, 189, 248, 0.1);
            border-left: 4px solid #38bdf8;
            padding: 24px;
            border-radius: 8px;
            margin: 24px 0;
        }

        .key-points {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
            margin-top: 32px;
        }

        .point {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .point-number {
            background: linear-gradient(135deg, #38bdf8, #a78bfa);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .footer {
            position: absolute;
            bottom: 24px;
            right: 32px;
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.6);
        }
    </style>
</head>
<body>
    <div class="slide">
        <!-- 背景元素 -->
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <!-- 页眉 -->
        <div style="margin-bottom: 24px;">
            <h1 class="gradient-text">人工智能定义</h1>
            <div style="width: 96px; height: 4px; background: linear-gradient(to right, #38bdf8, #a78bfa); border-radius: 2px;"></div>
        </div>
        
        <!-- 内容区域 -->
        <div style="display: grid; grid-template-columns: 12fr; gap: 24px;">
            <!-- 定义框 -->
            <div class="definition-box">
                <h2 style="color: #38bdf8; margin-bottom: 16px;">核心定义</h2>
                <p style="font-size: 1.25rem; line-height: 1.6; margin: 0;">
                    人工智能（Artificial Intelligence, AI）是指由机器展现出的智能行为，
                    能够感知环境、学习知识、推理决策，并采取行动来实现特定目标的技术系统。
                </p>
            </div>
            
            <!-- 关键要点 -->
            <div class="key-points">
                <div class="point">
                    <div class="point-number">1</div>
                    <div>
                        <h3 style="margin: 0 0 8px 0; color: #38bdf8;">感知能力</h3>
                        <p style="margin: 0; opacity: 0.9;">通过传感器获取和处理环境信息</p>
                    </div>
                </div>
                
                <div class="point">
                    <div class="point-number">2</div>
                    <div>
                        <h3 style="margin: 0 0 8px 0; color: #a78bfa;">学习能力</h3>
                        <p style="margin: 0; opacity: 0.9;">从数据中提取模式和知识</p>
                    </div>
                </div>
                
                <div class="point">
                    <div class="point-number">3</div>
                    <div>
                        <h3 style="margin: 0 0 8px 0; color: #38bdf8;">推理能力</h3>
                        <p style="margin: 0; opacity: 0.9;">基于已有知识进行逻辑推断</p>
                    </div>
                </div>
                
                <div class="point">
                    <div class="point-number">4</div>
                    <div>
                        <h3 style="margin: 0 0 8px 0; color: #a78bfa;">决策能力</h3>
                        <p style="margin: 0; opacity: 0.9;">在复杂环境中做出最优选择</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="footer">第2页 - AI基础概念</div>
    </div>
</body>
</html>
