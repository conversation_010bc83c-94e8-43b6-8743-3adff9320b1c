// 手动测试文件提取功能
const fs = require('fs');
const path = require('path');

// 读取测试数据
const testData = fs.readFileSync(path.join(__dirname, 'test-data.txt'), 'utf8');
console.log('Test data loaded, length:', testData.length);

// 手动测试正则表达式
const regex = /```(html|markdown|md)\s*\{\s*filename\s*=\s*([^}]+)\s*\}\s*([\s\S]*?)\s*```/g;
console.log('Using regex pattern:', regex);

let match;
let matchCount = 0;
const files = [];

while ((match = regex.exec(testData)) !== null) {
  matchCount++;
  const blockType = match[1];
  const filename = match[2].trim();
  const content = match[3].trim();
  
  console.log(`Match ${matchCount}: ${blockType} file - filename: ${filename}`);
  console.log(`Content length: ${content.length}`);
  console.log(`Content preview: ${content.substring(0, 50)}...`);
  console.log('---');
  
  files.push({
    content,
    filename,
    contentType: blockType === 'html' ? 'html' : 'markdown'
  });
}

console.log(`Total files extracted: ${files.length}`);

// 如果正则表达式方法失败，尝试使用字符串分割方法
if (files.length === 0) {
  console.log('No files extracted with regex, trying string split method');
  
  // 尝试不同的分隔符模式
  const htmlPatterns = [
    '```html{filename=',
    '```html {filename=',
    '```html{ filename=',
    '```html { filename='
  ];
  
  let startIndices = [];
  
  // 找到所有可能的分隔符位置
  htmlPatterns.forEach(pattern => {
    let pos = 0;
    while ((pos = testData.indexOf(pattern, pos)) !== -1) {
      startIndices.push(pos);
      pos += pattern.length;
    }
  });
  
  // 按照位置排序
  startIndices.sort((a, b) => a - b);
  
  // 处理每个匹配的位置
  for (const startPos of startIndices) {
    // 找到文件名结束位置
    const filenameStartPos = testData.indexOf('=', startPos) + 1;
    const filenameEndPos = testData.indexOf('}', filenameStartPos);
    if (filenameEndPos === -1) continue;
    
    // 提取文件名
    const filename = testData.substring(filenameStartPos, filenameEndPos).trim();
    
    // 找到代码块结束位置
    const contentStartPos = filenameEndPos + 1;
    const contentEndPos = testData.indexOf('```', contentStartPos);
    if (contentEndPos === -1) continue;
    
    // 提取内容
    const content = testData.substring(contentStartPos, contentEndPos).trim();
    console.log(`HTML file found with split method - filename: ${filename}`);
    console.log(`Content length: ${content.length}`);
    console.log(`Content preview: ${content.substring(0, 50)}...`);
    console.log('---');
    
    files.push({
      content,
      filename,
      contentType: 'html'
    });
  }
  
  console.log(`Total files extracted after split method: ${files.length}`);
}

// 打印最终结果
console.log('Final extracted files:');
files.forEach((file, index) => {
  console.log(`${index + 1}. ${file.filename} (${file.contentType}) - length: ${file.content.length}`);
});
