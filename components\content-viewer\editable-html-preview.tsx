'use client';

import React, { useEffect, useRef, useState } from 'react';
import grapesjs from 'grapesjs';
import 'grapesjs/dist/css/grapes.min.css';
import gjsPresetWebpage from 'grapesjs-preset-webpage';

interface EditableHtmlPreviewProps {
  content: string;
  onSave: (content: string) => void;
  onClose: () => void;
  onEditorReady?: (editor: any) => void;
}

const EditableHtmlPreview: React.FC<EditableHtmlPreviewProps> = ({ 
  content, 
  onSave,
  onClose,
  onEditorReady
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [editor, setEditor] = useState<any>(null);
  
  // 初始化GrapesJS编辑器
  useEffect(() => {
    if (!editorRef.current) return;
    
    // 确保内容是完整的HTML文档
    let htmlContent = content;
    if (!content.includes('<!DOCTYPE html>') && !content.includes('<html')) {
      console.log('Content is not a complete HTML document, wrapping it');
      htmlContent = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HTML Preview</title>
  <style>
    body { font-family: system-ui, sans-serif; line-height: 1.5; padding: 1rem; }
    pre { background: #f5f5f5; padding: 1rem; border-radius: 0.25rem; overflow: auto; }
    code { font-family: monospace; }
  </style>
</head>
<body>
  ${content}
</body>
</html>`;
    }
    
    // 初始化GrapesJS
    const editor = grapesjs.init({
      container: editorRef.current,
      fromElement: false,
      height: '100%',
      width: '100%',
      storageManager: false,
      plugins: [gjsPresetWebpage],
      pluginsOpts: {
        'grapesjs-preset-webpage': {
          // 预设选项
          blocks: true,
          navbarOpts: false,
          countdownOpts: false,
          formsOpts: false,
          exportOpts: false,
          aviaryOpts: false,
          filestackOpts: false,
        }
      },
      // 完全禁用编辑器默认面板和工具栏
      // 设置画布占据所有可用空间
      canvas: {
        styles: [
          'https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css'
        ],
        frameStyle: `
          body { margin: 0; padding: 0; }
          .gjs-frame { left: 0 !important; margin: 0 !important; padding: 0 !important; }
          .gjs-dashed { margin: 0 !important; padding: 0 !important; }
          * { box-sizing: border-box; }
        `
      },

      // 禁用默认的编辑器左侧面板
      selectorManager: { appendTo: '.styles-container' },
      traitManager: { appendTo: '.traits-container' },
      layerManager: { appendTo: '.layers-container' },
      deviceManager: { devices: [] },

      // 完全禁用所有面板
      panels: { defaults: [] },
      // 自定义CSS
      style: `
        .gjs-cv-canvas {
          width: 100% !important;
          height: 100% !important;
          left: 0 !important;
          top: 0 !important;
          padding: 0 !important;
        }
        .gjs-frame-wrapper {
          padding: 0 !important;
          margin: 0 !important;
          left: 0 !important;
        }
        .gjs-editor {
          overflow: hidden;
        }
        .gjs-pn-views-container {
          padding: 0 !important;
          left: 0 !important;
        }
        .gjs-pn-views {
          left: 0 !important;
          padding: 0 !important;
          border: none !important;
        }
        /* 强制覆盖编辑器画布容器的样式 */
        #gjs {
          border: none !important;
          overflow: hidden !important;
        }
        .gjs-cv-canvas .gjs-frame {
          left: 0 !important;
          top: 0 !important;
          width: 100% !important;
          margin-left: 0 !important;
        }
        .gjs-one-bg {
          background-color: transparent !important;
        }
        /* 移除所有空白和边距 */
        .gjs-frame-wrapper, .gjs-frame {
          margin: 0 !important;
          padding: 0 !important;
          left: 0 !important;
          right: 0 !important;
          width: 100% !important;
        }
        /* 设置编辑器容器的宽度为100% */
        .canvas-container {
          width: 100% !important;
          padding: 0 !important;
          margin: 0 !important;
          overflow: hidden !important;
        }
        .panel-top-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 10px;
          height: 50px;
          background-color: #f5f5f5;
          border-bottom: 1px solid #ddd;
        }
        .panel-logo {
          font-weight: bold;
          font-size: 16px;
        }
        .panel-actions {
          display: flex;
          gap: 10px;
        }
        .btn-save, .btn-cancel {
          padding: 6px 12px;
          border-radius: 4px;
          border: none;
          cursor: pointer;
          font-size: 14px;
        }
        .btn-save {
          background-color: #4f46e5;
          color: white;
        }
        .btn-cancel {
          background-color: #e5e7eb;
          color: #374151;
        }
        .panel-devices-content {
          display: flex;
          gap: 10px;
          padding: 10px;
          background-color: #f5f5f5;
          border-bottom: 1px solid #ddd;
        }
        .device-desktop, .device-tablet, .device-mobile {
          padding: 5px 10px;
          border-radius: 4px;
          cursor: pointer;
          background-color: #e5e7eb;
        }
        .device-desktop.active, .device-tablet.active, .device-mobile.active {
          background-color: #4f46e5;
          color: white;
        }
        .panel-switcher-content {
          display: flex;
          gap: 10px;
          padding: 10px;
          background-color: #f5f5f5;
          border-bottom: 1px solid #ddd;
        }
        .switcher-tab {
          padding: 5px 10px;
          border-radius: 4px;
          cursor: pointer;
          background-color: #e5e7eb;
        }
        .switcher-tab.active {
          background-color: #4f46e5;
          color: white;
        }
      `,
    });
    
    // 加载HTML内容
    editor.setComponents(htmlContent);
    
    // 设置编辑器实例
    setEditor(editor);
    
    // 如果提供了onEditorReady回调，则传递编辑器实例
    if (onEditorReady) {
      onEditorReady(editor);
    }
    
    // 添加事件监听器
    const btnSave = editorRef.current?.querySelector('.btn-save');
    const btnCancel = editorRef.current?.querySelector('.btn-cancel');
    
    if (btnSave) {
      btnSave.addEventListener('click', () => {
        // 获取HTML内容
        const html = editor.getHtml();
        const css = editor.getCss();
        
        // 合并HTML和CSS
        let fullHtml = html;
        if (css) {
          // 检查是否已有style标签
          if (fullHtml.includes('</head>')) {
            fullHtml = fullHtml.replace('</head>', `<style>${css}</style></head>`);
          } else {
            fullHtml = `<style>${css}</style>${fullHtml}`;
          }
        }
        
        // 保存内容
        onSave(fullHtml);
      });
    }
    
    if (btnCancel) {
      btnCancel.addEventListener('click', () => {
        onClose();
      });
    }
    
    // 设备切换
    const deviceBtns = editorRef.current?.querySelectorAll('[data-device]') || [];
    deviceBtns?.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const device = (e.currentTarget as HTMLElement).getAttribute('data-device');
        deviceBtns?.forEach(b => b.classList.remove('active'));
        (e.currentTarget as HTMLElement).classList.add('active');
        
        switch (device) {
          case 'desktop':
            editor.setDevice('Desktop');
            break;
          case 'tablet':
            editor.setDevice('Tablet');
            break;
          case 'mobile':
            editor.setDevice('Mobile');
            break;
        }
      });
    });
    
    // 标签切换
    const tabBtns = editorRef.current?.querySelectorAll('[data-tab]');
    const tabContainers = {
      blocks: editorRef.current?.querySelector('.blocks-container'),
      style: editorRef.current?.querySelector('.styles-container'),
      layers: editorRef.current?.querySelector('.layers-container'),
    };
    
    tabBtns?.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tab = (e.currentTarget as HTMLElement).getAttribute('data-tab');
        tabBtns?.forEach(b => b.classList.remove('active'));
        (e.currentTarget as HTMLElement).classList.add('active');
        
        // 隐藏所有容器
        Object.values(tabContainers).forEach(container => {
          if (container) (container as HTMLElement).style.display = 'none';
        });
        
        // 显示选中的容器
        if (tab && tab in tabContainers && tabContainers[tab as keyof typeof tabContainers]) {
          const container = tabContainers[tab as keyof typeof tabContainers];
          if (container) (container as HTMLElement).style.display = 'block';
        }
        
        // 根据标签加载相应的面板
        if (editor && editor.Panels) {
          const panel = editor.Panels.getPanel('views');
          if (panel) {
            switch (tab) {
              case 'blocks':
                panel.set('active', 0);
                break;
              case 'style':
                panel.set('active', 1);
                break;
              case 'layers':
                panel.set('active', 2);
                break;
            }
          }
        }
      });
    });
    
    // 默认选中桌面设备和组件标签
    const desktopBtn = editorRef.current?.querySelector('.device-desktop');
    const blocksTab = editorRef.current?.querySelector('.switcher-tab-blocks');
    if (desktopBtn) desktopBtn.classList.add('active');
    if (blocksTab) blocksTab.classList.add('active');
    
    // 清理函数
    return () => {
      if (editor) {
        editor.destroy();
      }
    };
  }, [content, onSave, onClose, onEditorReady]);
  
  // 检测是否为slide内容
  const isSlideContent = content && (content.includes('<div class="slide"') || content.includes('class="slide'));

  return (
    <div className="editable-html-preview h-full flex flex-col" style={{ minHeight: isSlideContent ? '700px' : '500px' }}>
      {/* 自定义顶部工具栏 - 隐藏 */}
      <div className="absolute top-0 left-0 right-0 z-50 bg-white/90 shadow-sm p-2 flex justify-between items-center border-b border-gray-200 hidden">
        <div className="font-bold text-lg">HTML编辑器</div>
        <div className="flex space-x-2">
          <button 
            onClick={() => {
              if (editor) {
                // 获取HTML内容
                const html = editor.getHtml();
                const css = editor.getCss();
                
                // 合并HTML和CSS
                let fullHtml = html;
                if (css) {
                  // 检查是否已有style标签
                  if (fullHtml.includes('</head>')) {
                    fullHtml = fullHtml.replace('</head>', `<style>${css}</style></head>`);
                  } else {
                    fullHtml = `<style>${css}</style>${fullHtml}`;
                  }
                }
                
                // 保存内容
                onSave(fullHtml);
              }
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            保存
          </button>
          <button 
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors"
          >
            取消
          </button>
        </div>
      </div>
      
      {/* 编辑器容器 - 移除侧边栏布局 */}
      <div className="editor-container flex flex-1 relative">
        {/* 隐藏侧边栏布局，但保留其元素供编辑器使用 */}
        <div className="sidebar absolute left-0 top-0 opacity-0 pointer-events-none" style={{ width: '0', height: '0', overflow: 'hidden' }}>
          <div className="panel-devices"></div>
          <div className="panel-switcher"></div>
          <div className="panel-blocks"></div>
          <div className="panel-style" style={{ display: 'none' }}></div>
          <div className="panel-layers" style={{ display: 'none' }}></div>
          <div className="traits-container" style={{ display: 'none' }}></div>
        </div>
        {/* 画布容器占据全宽 */}
        <div className="canvas-container w-full h-full">
          <div ref={editorRef} className="h-full w-full" style={{ minHeight: isSlideContent ? '600px' : '400px' }}></div>
        </div>
      </div>
    </div>
  );
};

export default EditableHtmlPreview;
