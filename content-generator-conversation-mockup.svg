<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect x="0" y="0" width="1200" height="800" fill="#f5f7fa"/>
  
  <!-- 页面容器 -->
  <rect x="50" y="50" width="1100" height="700" rx="8" fill="white" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 顶部导航栏 -->
  <rect x="50" y="50" width="1100" height="60" rx="8 8 0 0" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <text x="100" y="85" font-family="Arial" font-size="20" font-weight="bold" fill="#333">内容生成器</text>
  
  <!-- 左侧面板 -->
  <rect x="50" y="110" width="400" height="640" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 右侧内容查看器 -->
  <rect x="450" y="110" width="700" height="640" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 左侧面板内容 -->
  <!-- 对话历史区域 -->
  <rect x="50" y="110" width="400" height="300" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 对话历史标题 -->
  <rect x="50" y="110" width="400" height="40" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <text x="80" y="135" font-family="Arial" font-size="16" font-weight="bold" fill="#333">对话历史</text>
  
  <!-- 用户消息 1 -->
  <rect x="80" y="160" width="300" height="40" rx="18" fill="#e1f5fe" stroke="#b3e5fc" stroke-width="1"/>
  <text x="100" y="185" font-family="Arial" font-size="14" fill="#333">我需要一个产品展示页面</text>
  <text x="390" y="155" font-family="Arial" font-size="12" fill="#666" text-anchor="end">你</text>
  
  <!-- AI回复 1 -->
  <rect x="120" y="210" width="300" height="40" rx="18" fill="#f5f5f5" stroke="#e0e0e0" stroke-width="1"/>
  <text x="140" y="235" font-family="Arial" font-size="14" fill="#333">请提供更多关于产品的信息</text>
  <text x="110" y="205" font-family="Arial" font-size="12" fill="#666">AI</text>
  
  <!-- 用户消息 2 -->
  <rect x="80" y="260" width="300" height="60" rx="18" fill="#e1f5fe" stroke="#b3e5fc" stroke-width="1"/>
  <text x="100" y="285" font-family="Arial" font-size="14" fill="#333">是一款智能手表，主打健康监测和</text>
  <text x="100" y="305" font-family="Arial" font-size="14" fill="#333">运动追踪功能</text>
  <text x="390" y="255" font-family="Arial" font-size="12" fill="#666" text-anchor="end">你</text>
  
  <!-- 清除对话按钮 -->
  <rect x="320" y="120" width="120" height="25" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="380" y="137" font-family="Arial" font-size="12" fill="#666" text-anchor="middle">清除对话</text>
  
  <!-- 内容类型和选项区域 -->
  <rect x="50" y="410" width="400" height="340" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 内容类型选择 -->
  <text x="80" y="440" font-family="Arial" font-size="16" font-weight="bold" fill="#333">内容类型</text>
  
  <!-- HTML按钮 -->
  <rect x="80" y="455" width="160" height="40" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="160" y="480" font-family="Arial" font-size="14" fill="white" text-anchor="middle">HTML</text>
  
  <!-- Markdown按钮 -->
  <rect x="260" y="455" width="160" height="40" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="340" y="480" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">Markdown</text>
  
  <!-- 高级选项 -->
  <text x="80" y="525" font-family="Arial" font-size="16" font-weight="bold" fill="#333">高级选项</text>
  
  <!-- 风格选择 -->
  <text x="80" y="555" font-family="Arial" font-size="14" fill="#555">风格</text>
  <rect x="80" y="565" width="340" height="40" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="100" y="590" font-family="Arial" font-size="14" fill="#333">简约现代</text>
  <path d="M400 585 L410 575 L420 585" fill="none" stroke="#666" stroke-width="2"/>
  
  <!-- 复杂度选择 -->
  <text x="80" y="625" font-family="Arial" font-size="14" fill="#555">复杂度</text>
  <rect x="80" y="635" width="340" height="40" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="100" y="660" font-family="Arial" font-size="14" fill="#333">中等</text>
  <path d="M400 655 L410 645 L420 655" fill="none" stroke="#666" stroke-width="2"/>
  
  <!-- 新消息输入区域 -->
  <rect x="50" y="750" width="400" height="60" rx="0 0 0 8" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <rect x="70" y="760" width="280" height="40" rx="20" fill="white" stroke="#d0d0d0" stroke-width="1"/>
  <text x="90" y="785" font-family="Arial" font-size="14" fill="#666" opacity="0.7">输入新的提示或修改建议...</text>
  
  <!-- 发送按钮 -->
  <rect x="360" y="760" width="70" height="40" rx="20" fill="#4a6cf7" stroke="none"/>
  <text x="395" y="785" font-family="Arial" font-size="14" fill="white" text-anchor="middle">发送</text>
  
  <!-- 右侧内容查看器 -->
  <!-- 标题栏 -->
  <rect x="450" y="110" width="700" height="50" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 标签切换按钮 -->
  <rect x="470" y="120" width="100" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="520" y="140" font-family="Arial" font-size="14" fill="white" text-anchor="middle">代码</text>
  
  <rect x="580" y="120" width="100" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="630" y="140" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">预览</text>
  
  <rect x="690" y="120" width="100" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="740" y="140" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">分屏</text>
  
  <!-- 操作按钮 -->
  <rect x="950" y="120" width="80" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="990" y="140" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">复制</text>
  
  <rect x="1040" y="120" width="80" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="1080" y="140" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">下载</text>
  
  <!-- 分割线 -->
  <line x1="800" y1="160" x2="800" y2="750" stroke="#e0e0e0" stroke-width="1" stroke-dasharray="5,5"/>
  
  <!-- 左侧代码区域 -->
  <rect x="460" y="170" width="330" height="570" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 代码行号 -->
  <rect x="460" y="170" width="30" height="570" fill="#f0f0f0" stroke="none"/>
  <text x="475" y="195" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">1</text>
  <text x="475" y="215" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">2</text>
  <text x="475" y="235" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">3</text>
  <text x="475" y="255" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">4</text>
  <text x="475" y="275" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">5</text>
  <text x="475" y="295" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">6</text>
  <text x="475" y="315" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">7</text>
  <text x="475" y="335" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">8</text>
  <text x="475" y="355" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">9</text>
  
  <!-- 代码内容 -->
  <text x="500" y="195" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;!DOCTYPE html&gt;</tspan></text>
  <text x="500" y="215" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;html&gt;</tspan></text>
  <text x="500" y="235" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;head&gt;</tspan></text>
  <text x="500" y="255" font-family="Consolas, monospace" font-size="12" fill="#333">  <tspan fill="#0000ff">&lt;title&gt;</tspan>智能手表 - 健康监测与运动追踪<tspan fill="#0000ff">&lt;/title&gt;</tspan></text>
  <text x="500" y="275" font-family="Consolas, monospace" font-size="12" fill="#333">  <tspan fill="#0000ff">&lt;style&gt;</tspan></text>
  <text x="500" y="295" font-family="Consolas, monospace" font-size="12" fill="#333">    body {</text>
  <text x="500" y="315" font-family="Consolas, monospace" font-size="12" fill="#333">      font-family: Arial, sans-serif;</text>
  <text x="500" y="335" font-family="Consolas, monospace" font-size="12" fill="#333">      margin: 0;</text>
  <text x="500" y="355" font-family="Consolas, monospace" font-size="12" fill="#333">      padding: 0;</text>
  
  <!-- 右侧预览区域 -->
  <rect x="810" y="170" width="330" height="570" rx="4" fill="white" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 预览内容 -->
  <text x="830" y="200" font-family="Arial" font-size="20" font-weight="bold" fill="#333">智能手表 - 健康监测与运动追踪</text>
  <rect x="830" y="220" width="290" height="1" fill="#e0e0e0"/>
  
  <rect x="830" y="240" width="290" height="150" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <text x="850" y="270" font-family="Arial" font-size="16" font-weight="bold" fill="#333">产品特点</text>
  <text x="850" y="300" font-family="Arial" font-size="14" fill="#555">• 全天候健康监测</text>
  <text x="850" y="325" font-family="Arial" font-size="14" fill="#555">• 多种运动模式追踪</text>
  <text x="850" y="350" font-family="Arial" font-size="14" fill="#555">• 智能通知提醒</text>
  
  <!-- 底部状态栏 -->
  <rect x="450" y="750" width="700" height="40" rx="0 0 8 0" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 状态信息 -->
  <text x="470" y="775" font-family="Arial" font-size="14" fill="#666">上次生成: 2023-06-15 14:30</text>
  
  <!-- 保存按钮 -->
  <rect x="1000" y="755" width="120" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="1060" y="775" font-family="Arial" font-size="14" fill="white" text-anchor="middle">保存内容</text>
  
  <!-- 历史记录弹窗 (隐藏状态) -->
  <g opacity="0">
    <rect x="300" y="200" width="600" height="400" rx="8" fill="white" stroke="#e0e0e0" stroke-width="2" filter="drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1))"/>
    <text x="330" y="240" font-family="Arial" font-size="18" font-weight="bold" fill="#333">历史记录</text>
    <line x1="300" y1="260" x2="900" y2="260" stroke="#e0e0e0" stroke-width="1"/>
    
    <!-- 历史记录项目 -->
    <rect x="320" y="280" width="560" height="60" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    <text x="340" y="305" font-family="Arial" font-size="14" font-weight="bold" fill="#333">智能手表产品页面</text>
    <text x="340" y="325" font-family="Arial" font-size="12" fill="#666">HTML • 2023-06-15 14:30</text>
    
    <rect x="320" y="350" width="560" height="60" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    <text x="340" y="375" font-family="Arial" font-size="14" font-weight="bold" fill="#333">项目说明文档</text>
    <text x="340" y="395" font-family="Arial" font-size="12" fill="#666">Markdown • 2023-06-14 10:15</text>
    
    <rect x="320" y="420" width="560" height="60" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    <text x="340" y="445" font-family="Arial" font-size="14" font-weight="bold" fill="#333">团队介绍页面</text>
    <text x="340" y="465" font-family="Arial" font-size="12" fill="#666">HTML • 2023-06-12 16:45</text>
    
    <!-- 关闭按钮 -->
    <circle cx="880" cy="230" r="15" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
    <text x="880" y="235" font-family="Arial" font-size="16" fill="#666" text-anchor="middle">×</text>
  </g>
  
  <!-- 加载状态 (隐藏状态) -->
  <g opacity="0">
    <rect x="0" y="0" width="1200" height="800" fill="rgba(0,0,0,0.5)"/>
    <rect x="450" y="350" width="300" height="100" rx="8" fill="white" stroke="#e0e0e0" stroke-width="1"/>
    <text x="600" y="400" font-family="Arial" font-size="16" fill="#333" text-anchor="middle">正在生成内容，请稍候...</text>
    
    <!-- 加载动画 -->
    <circle cx="560" cy="430" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0s"/>
    </circle>
    <circle cx="580" cy="430" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0.2s"/>
    </circle>
    <circle cx="600" cy="430" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0.4s"/>
    </circle>
    <circle cx="620" cy="430" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>
    </circle>
    <circle cx="640" cy="430" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0.8s"/>
    </circle>
  </g>
</svg>
