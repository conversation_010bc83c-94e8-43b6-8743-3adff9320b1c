// 实际的AI回复内容
const realAiResponse = `当然可以！以下是一个简单的 "Hello, World!" 示例，分别使用 HTML 和 Markdown 格式。 ### HTML 示例 \`\`\`html <!DOCTYPE html> <html lang="zh"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>Hello World</title> </head> <body> <h1>Hello, World!</h1> </body> </html> \`\`\` ### Markdown 示例 \`\`\`markdown # Hello, World! \`\`\` 你可以将上述代码复制到相应的文件中查看效果！`;

// 提取HTML代码的函数
function extractHtmlFromMessage(message) {
  console.log('Extracting HTML from message...');
  console.log('Message length:', message.length);
  console.log('Message preview:', message.substring(0, 100) + '...');
  
  // 尝试提取HTML代码块
  const htmlMatch = message.match(/```html\s*([\s\S]*?)\s*```/);
  if (htmlMatch && htmlMatch[1]) {
    const code = htmlMatch[1].trim();
    console.log('Found HTML code block, length:', code.length);
    console.log('HTML code preview:', code.substring(0, 100) + '...');
    
    // 检查是否是完整HTML
    if (code.includes('<!DOCTYPE html>') || code.includes('<html')) {
      return code;
    } else {
      // 如果不是完整HTML，包装它
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hello World</title>
</head>
<body>
  ${code}
</body>
</html>`;
    }
  }
  
  // 尝试提取无语言指定的代码块
  const genericMatch = message.match(/```\s*([\s\S]*?)\s*```/);
  if (genericMatch && genericMatch[1]) {
    const code = genericMatch[1].trim();
    console.log('Found generic code block, length:', code.length);
    console.log('Generic code preview:', code.substring(0, 100) + '...');
    
    // 检查是否是HTML
    if (code.includes('<') && code.includes('>')) {
      // 检查是否是完整HTML
      if (code.includes('<!DOCTYPE html>') || code.includes('<html')) {
        return code;
      } else {
        // 如果不是完整HTML，包装它
        return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hello World</title>
</head>
<body>
  ${code}
</body>
</html>`;
      }
    }
  }
  
  // 尝试直接从消息中提取HTML
  if (message.includes('<!DOCTYPE html>') || message.includes('<html')) {
    // 尝试提取完整的HTML文档
    const docTypeIndex = message.indexOf('<!DOCTYPE html>');
    const htmlStartIndex = message.indexOf('<html');
    const startIndex = docTypeIndex >= 0 ? docTypeIndex : htmlStartIndex;
    
    if (startIndex >= 0) {
      const htmlEndIndex = message.indexOf('</html>', startIndex);
      if (htmlEndIndex >= 0) {
        const html = message.substring(startIndex, htmlEndIndex + 7);
        console.log('Extracted complete HTML document, length:', html.length);
        console.log('HTML document preview:', html.substring(0, 100) + '...');
        return html;
      }
    }
  }
  
  console.log('No HTML code found');
  return null;
}

// 提取Markdown代码的函数
function extractMarkdownFromMessage(message) {
  console.log('Extracting Markdown from message...');
  
  // 尝试提取Markdown代码块
  const mdMatch = message.match(/```markdown\s*([\s\S]*?)\s*```/);
  if (mdMatch && mdMatch[1]) {
    const code = mdMatch[1].trim();
    console.log('Found Markdown code block, length:', code.length);
    console.log('Markdown code preview:', code.substring(0, 100) + '...');
    return code;
  }
  
  // 尝试提取md代码块
  const mdBlockMatch = message.match(/```md\s*([\s\S]*?)\s*```/);
  if (mdBlockMatch && mdBlockMatch[1]) {
    const code = mdBlockMatch[1].trim();
    console.log('Found md code block, length:', code.length);
    console.log('md code preview:', code.substring(0, 100) + '...');
    return code;
  }
  
  // 尝试提取无语言指定的代码块
  const genericMatch = message.match(/```\s*([\s\S]*?)\s*```/);
  if (genericMatch && genericMatch[1]) {
    const code = genericMatch[1].trim();
    console.log('Found generic code block for Markdown, length:', code.length);
    console.log('Generic code preview:', code.substring(0, 100) + '...');
    return code;
  }
  
  // 如果没有找到代码块，尝试提取以#开头的内容
  if (message.includes('# ')) {
    const lines = message.split('\n');
    const titleLineIndex = lines.findIndex(line => line.trim().startsWith('# '));
    
    if (titleLineIndex >= 0) {
      const markdown = lines.slice(titleLineIndex).join('\n');
      console.log('Extracted Markdown content, length:', markdown.length);
      console.log('Markdown content preview:', markdown.substring(0, 100) + '...');
      return markdown;
    }
  }
  
  console.log('No Markdown code found');
  return null;
}

// 测试HTML提取
console.log('=== Testing HTML extraction from real AI response ===');
const extractedHtml = extractHtmlFromMessage(realAiResponse);
console.log('Extracted HTML:', extractedHtml);
console.log('HTML length:', extractedHtml ? extractedHtml.length : 0);
console.log('\n');

// 测试Markdown提取
console.log('=== Testing Markdown extraction from real AI response ===');
const extractedMarkdown = extractMarkdownFromMessage(realAiResponse);
console.log('Extracted Markdown:', extractedMarkdown);
console.log('Markdown length:', extractedMarkdown ? extractedMarkdown.length : 0);

// 测试提取所有代码块
console.log('\n=== Testing all code blocks extraction ===');
const allCodeBlocks = realAiResponse.match(/```[\s\S]*?```/g) || [];
console.log('All code blocks found:', allCodeBlocks.length);
allCodeBlocks.forEach((block, index) => {
  console.log(`Code block ${index + 1}:`, block);
});
