# 任务规划调试文档

## 问题现状

用户输入"写个大模型的PPT"后，AI正确进行了任务规划，但系统没有创建Todo.md文件，也没有自动开始执行任务。

从日志可以看出：
```
流式聊天完成: {
  content: '我将为您规划一个关于大模型的PPT演示文稿。根据内容复杂度，这是一个中等需求，需要拆分为多个任务。\n' +
    '\n' +
    '【任务1】创建封面幻灯片\n' +
    '- 包含主标题"大模型技术解析"、副标题"人工智能新时代的核心驱动力"\n' +
    '- 使用渐变背景和现代设计风格\n' +
    '- 创建文件：slide-1-cover.html\n' +
    '\n' +
    '【任务2】创建大模型定义幻灯片\n' +
    // ... 更多任务
}
```

AI确实生成了包含【任务1】到【任务6】的规划内容，但系统没有正确处理。

## 调试步骤

### 1. 添加详细日志

在任务规划判断和提取过程中添加了详细的调试日志：

```tsx
// 智能判断是否需要进行任务规划
console.log('开始判断是否需要任务规划...');
console.log('用户输入:', userMessage.content);
console.log('AI回复长度:', finalContent.length);
console.log('AI回复前100字符:', finalContent.substring(0, 100));

const shouldExtractTasks = shouldPerformTaskPlanning(userMessage.content, finalContent);
console.log('任务规划判断结果:', shouldExtractTasks);

if (shouldExtractTasks) {
  console.log('开始提取任务...');
  // 处理任务提取
  const extractedTasks = extractTasksFromResponse(finalContent);
  console.log('提取到的任务数量:', extractedTasks.length);
  console.log('提取到的任务:', extractedTasks);
  
  if (extractedTasks.length > 0) {
    console.log('设置任务状态...');
    console.log('创建Todo.md文件...');
    console.log('准备执行第一个任务...');
  }
}
```

### 2. 预期的调试输出

如果系统正常工作，应该看到以下日志：

```
开始判断是否需要任务规划...
用户输入: 写个大模型的PPT
AI回复长度: 1234
AI回复前100字符: 我将为您规划一个关于大模型的PPT演示文稿。根据内容复杂度，这是一个中等需求，需要拆分为多个任务。

【任务1】创建封面幻灯片

任务规划判断: {
  hasTaskMarkers: true,
  hasProjectKeyword: true,
  hasProjectType: true,
  isNewProject: true,
  isFeedback: false,
  isNewConversation: true,
  executionPhase: "idle",
  shouldPlan: true,
  userInputLength: 8,
  userInput: "写个大模型的PPT..."
}

任务规划判断结果: true
开始提取任务...
从模型响应中提取任务...
提取到 6 个顶级任务，总计 6 个任务（含子任务）
提取到的任务数量: 6
提取到的任务: [
  {
    id: "task_xxx",
    number: 1,
    description: "创建封面幻灯片\n- 包含主标题...",
    status: "pending",
    level: 0,
    context: "- 包含主标题..."
  },
  // ... 更多任务
]
设置任务状态...
创建Todo.md文件...
准备执行第一个任务...
```

### 3. 可能的问题点

#### 问题1：判断逻辑失败
如果看到`任务规划判断结果: false`，说明判断逻辑有问题：
- 检查`hasTaskMarkers`是否为true
- 检查`isNewProject`是否为true
- 检查`isFeedback`是否为false

#### 问题2：任务提取失败
如果看到`提取到的任务数量: 0`，说明任务提取逻辑有问题：
- 检查正则表达式是否匹配【任务X】格式
- 检查AI回复的实际格式

#### 问题3：状态更新失败
如果任务提取成功但Todo.md没有创建：
- 检查`setTasks`是否正确调用
- 检查`updateTodoFile(true)`是否正确执行
- 检查React状态更新是否生效

## 修复位置

**文件**：`app/content-generator/content-generator-stream.tsx`
**修改行数**：第306-342行

## 测试步骤

1. **清空控制台**：确保能看到新的日志输出
2. **输入测试**：输入"写个大模型的PPT"
3. **观察日志**：按照预期输出检查每个步骤
4. **定位问题**：根据日志输出确定问题所在

## 常见问题排查

### 问题A：shouldPerformTaskPlanning返回false

**可能原因**：
- 用户输入不满足项目需求条件
- AI回复不包含任务标记
- 被误识别为反馈

**排查方法**：
检查`任务规划判断`日志中的各个字段值

### 问题B：extractTasksFromResponse返回空数组

**可能原因**：
- 正则表达式不匹配AI回复格式
- AI回复格式与预期不符

**排查方法**：
1. 检查AI回复的实际内容
2. 测试正则表达式`/【任务(\d+(?:\.\d+)*)】([^\n]+)/g`
3. 检查是否需要调整匹配规则

### 问题C：状态更新不生效

**可能原因**：
- React状态更新异步问题
- 组件重渲染问题
- 依赖项缺失

**排查方法**：
1. 检查`setTasks`调用是否正确
2. 检查`tasks`状态是否更新
3. 检查`updateTodoFile`是否被调用

## 预期修复结果

修复后，用户输入"写个大模型的PPT"应该：

1. ✅ 正确识别为项目需求
2. ✅ 成功提取6个任务
3. ✅ 创建Todo.md文件
4. ✅ 自动开始执行第一个任务
5. ✅ 在控制台输出完整的调试信息

## 后续优化

1. **移除调试日志**：修复完成后移除详细的调试日志
2. **错误处理**：添加更好的错误处理和用户提示
3. **性能优化**：优化任务提取和状态更新的性能
4. **用户反馈**：提供更好的用户反馈机制

## 总结

通过添加详细的调试日志，我们可以准确定位问题所在：
- 是判断逻辑的问题
- 是任务提取的问题
- 还是状态更新的问题

这样可以有针对性地进行修复，确保任务规划功能正常工作。
