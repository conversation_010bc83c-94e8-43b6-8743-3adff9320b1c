# Easy Coder 内容生成智能体系统架构与核心设计（修订版）

---

## 1. 系统架构设计

Easy Coder 是一个基于 Next.js 15.x + React 19 + Tailwind CSS 的 AI 驱动内容/代码生成平台，聚焦“面向过程文件的AIGC原生内容生成”。系统采用前后端分离架构，前端以 `app/content-generator/content-generator-client.tsx` 为主入口，负责任务规划、执行、文件提取、版本管理、模型调用、UI交互等全流程。后端通过 `/api/chat` 接口对接 LLM（如 gpt-4o），实现智能内容生成与多轮对话。

**架构分层：**
- **UI层**：对话面板、内容预览、AI设置、批量操作、异常提示等，负责用户交互与反馈。
- **业务逻辑层**：任务规划与执行、文件提取与管理、版本控制、模型参数配置、操作历史与撤销/重做等。
- **数据层**：会话、任务、文件、操作、模型配置等状态管理，部分持久化（如 cookies、memory-bank/、数据库）。
- **AI服务层**：通过 provider-factory 机制 API 调用 LLM，支持多模型/provider切换与参数自定义，集成分析与监控（Helicone、Plausible）。

**主要目录结构补充：**
- `app/`：主应用代码，含 content-generator、全局页面、API 路由等。
- `components/`：全局复用组件。
- `hooks/`：全局复用钩子。
- `lib/`：工具函数、AI接口、状态管理、provider-factory、streaming、constants、prompt-modules等。
- `memory-bank/`：架构、决策、进度、模式等文档与知识库，含 TodoList.md、summary.md 等持久化文件。
- `public/`：静态资源。
- `docs/`：技术文档。
- `tests/`：测试文件。
- `prisma/`：数据库模型与迁移。

---

## 2. 技术约束与开发规范

- **前端框架**：Next.js 15.x，React 19，TypeScript 严格模式，Prisma ORM。
- **样式**：Tailwind CSS，响应式设计。
- **AI接口**：OpenAI 兼容 API，支持多模型/provider，provider-factory 动态扩展。
- **状态管理**：React Hooks + Zustand（useAIStore、useFileStore等）。
- **代码质量**：ESLint、Prettier、JSDoc 注释、TypeScript 类型安全、测试覆盖率要求。
- **安全性**：前后端接口鉴权，代码沙箱隔离（Sandpack），API密钥管理。
- **监控与分析**：集成 Helicone、Plausible 进行请求与行为分析。
- **开发规范**：
  - 组件模块化、职责单一，业务逻辑与UI解耦。
  - 重要操作与状态变更均有详细日志与 FileOperation 记录。
  - 任务、文件、版本等核心对象有严格的数据结构定义。
  - 变更需配套注释与文档，JSDoc 注释覆盖复杂逻辑。
  - 依赖项需定期升级，保持兼容性。
  - 测试文件需覆盖主要业务流程，保证回归安全。

---

## 3. 核心文件目录与功能摘要

| 路径 | 主要功能 |
| ---- | -------- |
| `app/content-generator/content-generator-client.tsx` | 主页面，集成任务规划、执行、文件管理、UI交互等全部核心逻辑 |
| `app/content-generator/content-generator-client-stream.tsx` | 流式内容生成主入口变体，提升大文件/多任务体验 |
| `app/content-generator/components/conversation-panel.tsx` | 对话面板，用户输入、消息流、任务展示、补充/重试 |
| `app/content-generator/components/content-viewer-panel.tsx` | 文件/内容预览与版本切换，支持分屏/代码/可视化多模式 |
| `app/content-generator/components/file-viewer.tsx` | 文件预览组件，支持多类型内容展示、分屏对比 |
| `app/content-generator/components/streaming-file-preview.tsx` | 流式文件预览，提升大文件/多文件体验 |
| `app/content-generator/types.ts` | 任务、文件、操作、模型等核心类型定义 |
| `app/content-generator/task-functions.ts` | 任务执行、总结等AI调用与业务逻辑 |
| `app/content-generator/promptTemplates.ts` | 生成内容的提示词模板，分层结构 |
| `lib/code-extractor.ts` | 从AI回复中提取代码/文件的工具函数 |
| `lib/ai-store.ts` | AI模型与参数的全局状态管理（Zustand） |
| `lib/ai-client.ts` | AI接口请求封装 |
| `lib/ai-config.ts` | AI配置持久化与 cookies 管理（AIConfigManager）|
| `lib/constants.ts` | 全局常量定义 |
| `lib/provider-factory.ts` | LLM provider 动态扩展与统一调用 |
| `lib/streaming/` | 流式处理相关工具与类型 |
| `memory-bank/` | 架构、决策、进度、模式等文档与知识库，含 TodoList.md、summary.md 等 |
| `public/` | 静态资源 |
| `docs/` | 技术文档 |
| `tests/` | 测试文件 |

> 说明：如有全局复用组件/钩子被 content-generator 直接依赖，建议在表格后补充说明其作用与依赖关系。

---

## 4. 文件依赖关系

```mermaid
graph TD
  A[content-generator-client.tsx]
  B[conversation-panel.tsx]
  C[content-viewer-panel.tsx]
  D[types.ts]
  E[task-functions.ts]
  F[promptTemplates.ts]
  G[code-extractor.ts]
  H[ai-store.ts]
  I[ai-client.ts]

  A --> B
  A --> C
  A --> D
  A --> E
  A --> F
  A --> G
  A --> H
  A --> I
  B --> D
  B --> H
  C --> D
  E --> D
  G --> D
  H --> I
```

- 主入口 `content-generator-client.tsx` 依赖所有核心业务与UI组件。
- conversation-panel.tsx 依赖 ai-store.ts（全局AI状态管理）。
- code-extractor.ts 若直接 import types.ts，已补充 G --> D。
- 任务、文件、操作等类型定义集中于 `types.ts`。
- 任务执行、AI调用、文件提取等分别解耦为独立模块。
- 状态管理与AI参数配置通过 `ai-store.ts` 实现全局共享。

---

## 5. 核心设计说明

### 5.1 任务规划与执行
- 用户输入需求后，系统自动调用 LLM 进行任务拆解（多级任务树），并生成任务清单（memory-bank/TodoList.md 持久化）。
- 任务类型定义（Task、TaskStatus、subtasks、dependencies）支持多级任务树、状态流转、递归子任务。
- 任务执行阶段自动调用 LLM 生成内容，提取文件，更新任务与文件状态，支持自动与手动触发。
- 任务执行与AI调用、文件提取的自动化流程，具体状态流转与触发点详见类型定义与业务逻辑实现。

### 5.2 文件提取与管理
- 支持从AI回复中提取单/多文件（支持html/markdown等），自动命名与类型识别（contentType）。
- 文件对象（GeneratedFile）包含多版本历史（versions[]）、order（文件顺序）、viewMode，支持版本切换、回滚、合并。
- 文件操作（创建/更新/切换版本）均有 FileOperation 结构详细记录，便于追溯与调试。
- 文件类型如需扩展，需在类型定义与提取逻辑中补充说明。

### 5.3 文件版本管理
- 每个文件维护完整版本链（GeneratedFile.versions[]），currentVersionIndex 支持切换，taskNumber/taskDescription 支持与任务关联。
- 版本切换时自动合并同名文件历史为前端逻辑，具体实现方式见 UI 逻辑与状态管理。
- 版本变更通过自定义事件广播（如 Zustand 状态变更、UI订阅），确保UI与状态同步。
- 版本描述与任务关联，便于追溯。

### 5.4 模型管理
- 支持多模型/provider切换（如gpt-4o），参数可自定义（如上下文窗口、风格、复杂度等）。
- 模型配置全局共享，影响任务执行与内容生成。
- AIConfigManager 支持 AI 配置 cookies 持久化。
- 支持自定义模型（CustomModel）与默认模型的管理差异。

### 5.5 数据存储
- 会话、任务、文件、操作等状态主要存于前端内存（Zustand），部分（如AI配置、TodoList.md、summary.md等）持久化于 cookies 或 memory-bank/。
- 重要文档如 summary.md、TodoList.md 的生成与更新机制详见 memory-bank/ 相关实现。
- 明确区分“前端内存状态”与“memory-bank/文件持久化”两类数据存储方式及其适用场景。

### 5.6 提示词设计
- promptTemplates.ts 采用分层模板，自动拼接用户输入与系统指令，确保AI输出结构化、可解析。
- 支持多文件、多任务、内容类型等多维度指令。
- 示例：
  ```ts
  // promptTemplates.ts
  export const baseTemplate = ({ userInput, systemInstruction }) => `
    [系统指令]
    ${systemInstruction}
    [用户输入]
    ${userInput}
  `;
  ```
- 分层结构与拼接流程详见 promptTemplates.ts 及相关业务逻辑。

### 5.7 关系梳理

```mermaid
flowchart TD
  UserInput[用户输入]
  TaskPlan[任务规划]
  TaskExec[任务执行]
  FileExtract[文件提取]
  FileManage[文件管理/版本]
  ModelManage[模型管理]
  DataStore[数据存储]
  PromptDesign[提示词设计]
  UI[UI交互]

  UserInput --> TaskPlan
  TaskPlan --> TaskExec
  TaskExec --> FileExtract
  FileExtract --> FileManage
  TaskExec --> FileManage
  FileManage --> DataStore
  TaskPlan --> DataStore
  ModelManage --> TaskExec
  PromptDesign --> TaskExec
  UI --> UserInput
  UI --> FileManage
  UI --> TaskPlan
  UI --> ModelManage
```
- 事件流/状态变更在各模块间起到联动作用（如任务-文件-UI的同步），详见状态管理与事件广播实现。

---

## 6. 系统交互图（核心流程）

```mermaid
sequenceDiagram
  participant U as 用户
  participant UI as 前端UI
  participant LLM as LLM服务
  participant FM as 文件管理
  participant TM as 任务管理

  U->>UI: 输入需求
  UI->>LLM: 发送带指令的prompt
  LLM-->>UI: 返回任务拆解/内容/文件
  UI->>TM: 解析任务树，生成TodoList.md
  UI->>FM: 提取文件，初始化版本
  UI->>UI: 展示对话、任务、文件
  U->>UI: 选择任务/切换文件/切换版本/分屏/批量下载
  UI->>FM: 文件操作（新建/更新/切换版本/批量操作）
  UI->>LLM: 继续任务/补充内容（全局/单任务）
  FM-->>UI: 文件状态/版本变更事件
  TM-->>UI: 任务状态变更
  UI->>UI: 状态反馈与进度提示、异常与错误提示
```

- 内容预览区支持多模式切换（分屏/代码/可视化）、文件批量下载与导出。
- 任务与文件的状态实时反馈，所有操作均有进度与异常提示。
- 支持操作历史与撤销/重做（如文件版本回滚）。

---

## 7. 应用功能与UI交互说明

- **对话面板**：用户输入需求，查看AI回复，展示任务树与进度，支持任务状态切换、单任务补充/重试、异常与错误提示。
- **内容预览面板**：多文件预览，支持代码/预览/分屏模式，文件版本切换与回滚，分屏对比、批量操作与下载。
- **AI设置**：模型选择、风格/复杂度/上下文窗口等参数配置，区分可配置参数与只读参数。
- **任务清单（TodoList.md）**：自动生成与更新，反映任务拆解与完成进度，持久化于 memory-bank/。
- **文件操作记录**：每次文件创建/更新/切换均有 FileOperation 详细记录，便于追溯与调试。
- **版本管理**：每个文件支持多版本历史，切换时自动同步内容与描述，支持撤销/重做。
- **异常处理**：AI接口异常、文件提取失败、参数错误等均有弹窗或内联提示，便于用户定位问题。
- **状态反馈**：所有操作均有实时状态提示与进度反馈（如加载动画、状态标签）。

---

## 结论与建议

本系统以“任务驱动+文件原生+多版本管理”为核心，极大提升了AIGC内容生成的可控性、可追溯性与工程化能力。建议后续持续完善：
- 任务与文件的持久化与协作机制。
- 更丰富的AI模型与参数支持，provider-factory 动态扩展。
- 文件依赖与引用关系的可视化与自动分析。
- 任务执行与文件生成的自动化测试与回归保障。
- 文档持续结构化、细化，保持与实现同步，便于团队协作与维护。