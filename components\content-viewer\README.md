# Content Viewer 内容查看器组件

一个用于展示和预览HTML和Markdown内容的React组件，支持代码语法高亮和实时预览。

## 功能特点

- 支持HTML和Markdown内容
- 提供代码、预览和分屏三种视图模式
- 代码语法高亮
- 安全的HTML渲染
- 丰富的Markdown渲染
- 响应式设计
- 支持分屏比例调整

## 安装

```bash
npm install react-syntax-highlighter react-markdown
```

## 基本用法

```tsx
import dynamic from 'next/dynamic';

// 使用动态导入加载ContentViewer组件，并禁用SSR
const ContentViewer = dynamic(
  () => import('@/components/content-viewer/content-viewer'),
  { ssr: false }
);

// 在组件中使用
function MyComponent() {
  return (
    <div className="h-[600px]">
      <ContentViewer 
        content="<h1>Hello World</h1>"
        contentType="html"
        initialViewMode="split"
      />
    </div>
  );
}
```

## 文档

详细文档请参考：

- [英文文档](../../docs/content-viewer-documentation.md)
- [中文文档](../../docs/content-viewer-documentation-zh.md)

## 示例

查看示例页面：

- [基本示例](/test-content-viewer)
- [增强示例](/test-content-viewer-enhanced)

## 许可证

MIT
