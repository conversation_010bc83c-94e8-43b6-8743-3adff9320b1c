"use client";

import React from 'react';
import { StatusBarProps } from './types';

const StatusBar: React.FC<StatusBarProps> = ({
  contentType,
  lineCount,
  encoding,
  onCopyCode,
  onRefresh,
}) => {
  return (
    <div className="flex justify-between items-center px-3 py-1.5 bg-gray-50 border-t border-gray-200 text-xs text-gray-600">
      <div className="status-info">
        {contentType === 'html' ? 'HTML' : 'Markdown'} • {lineCount}行 • {encoding}
      </div>
      
      <div className="flex space-x-2">
        <button
          onClick={onCopyCode}
          className="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs hover:bg-gray-200 transition-colors"
        >
          复制代码
        </button>
        
        <button
          onClick={onRefresh}
          className="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs hover:bg-gray-200 transition-colors"
        >
          刷新
        </button>
      </div>
    </div>
  );
};

export default StatusBar;
