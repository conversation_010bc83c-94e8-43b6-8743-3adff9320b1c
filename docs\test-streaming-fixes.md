# 流式输出修复测试指南

## 测试环境准备

1. 确保项目已启动：`npm run dev`
2. 访问流式内容生成器页面：`http://localhost:3000/content-generator-stream`
3. 确保AI配置正确（API密钥等）

## 测试用例

### 测试用例1：右侧流式输出自动滚动

**目标**：验证右侧预览区域的流式内容能够自动滚动到最新内容

**测试步骤**：
1. 在左侧输入框输入：`创建一个包含大量内容的HTML页面，包含至少50行代码`
2. 点击发送，观察右侧预览区域
3. 检查流式输出过程中是否自动滚动到底部

**预期结果**：
- 右侧流式内容区域应该自动滚动，始终显示最新生成的内容
- 用户无需手动滚动即可看到最新内容

### 测试用例2：任务执行流式输出

**目标**：验证任务规划和执行过程中的流式输出显示

**测试步骤**：
1. 在左侧输入框输入：`创建一个完整的电商网站，包含首页、产品页面和购物车页面`
2. 等待任务规划完成
3. 观察任务执行过程中的流式输出

**预期结果**：
- 左侧任务列表应该显示任务规划结果
- 任务执行时应该显示流式输出内容
- 任务流式输出区域应该自动滚动
- 任务状态应该实时更新（待处理 → 进行中 → 已完成）

### 测试用例3：超长内容续写

**目标**：验证超长内容的自动续写和拼接功能

**测试步骤**：
1. 在左侧输入框输入：`创建一个包含详细CSS样式和JavaScript交互的完整HTML页面，至少包含1000行代码`
2. 观察是否触发续写机制
3. 检查最终生成的内容是否完整

**预期结果**：
- 当内容超过token限制时，应该自动触发续写
- 续写过程中应该显示"[继续生成中...]"提示
- 最终生成的内容应该完整，无重复或遗漏
- 续写的内容应该与前面的内容无缝连接

### 测试用例4：左侧对话区域流式输出滚动

**目标**：验证左侧对话区域的流式内容自动滚动

**测试步骤**：
1. 在左侧输入框输入：`解释React的生命周期方法，要详细说明每个方法的作用`
2. 观察左侧对话区域的流式输出
3. 检查是否自动滚动到最新内容

**预期结果**：
- 左侧对话区域的流式内容应该自动滚动
- 流式输出过程中应该显示打字动画
- 内容应该实时更新并保持可见

## 性能测试

### 测试用例5：大量内容性能测试

**目标**：验证大量流式内容的性能表现

**测试步骤**：
1. 输入要求生成大量内容的提示
2. 观察页面性能和响应速度
3. 检查内存使用情况

**预期结果**：
- 页面应该保持流畅，无明显卡顿
- 内存使用应该在合理范围内
- 自动滚动应该平滑进行

## 错误处理测试

### 测试用例6：网络中断恢复

**目标**：验证网络中断时的错误处理

**测试步骤**：
1. 开始一个流式生成任务
2. 在生成过程中断开网络连接
3. 恢复网络连接
4. 观察系统行为

**预期结果**：
- 应该显示适当的错误信息
- 系统应该能够优雅地处理错误
- 恢复连接后应该能够正常工作

### 测试用例7：API错误处理

**目标**：验证API错误的处理

**测试步骤**：
1. 配置错误的API密钥
2. 尝试生成内容
3. 观察错误处理

**预期结果**：
- 应该显示清晰的错误信息
- 不应该导致页面崩溃
- 用户应该能够重新配置并重试

## 兼容性测试

### 测试用例8：不同浏览器兼容性

**目标**：验证在不同浏览器中的表现

**测试浏览器**：
- Chrome
- Firefox
- Safari
- Edge

**测试内容**：
- 基本流式输出功能
- 自动滚动功能
- 任务执行流式显示

## 回归测试

### 测试用例9：原有功能验证

**目标**：确保修复没有破坏原有功能

**测试内容**：
1. 文件生成和下载
2. 版本管理
3. 内容预览切换
4. 设置面板功能

**预期结果**：
- 所有原有功能应该正常工作
- 没有新的bug引入

## 测试报告模板

```
测试日期：[日期]
测试人员：[姓名]
测试环境：[浏览器版本、操作系统等]

测试结果：
- 测试用例1：✅/❌ [备注]
- 测试用例2：✅/❌ [备注]
- 测试用例3：✅/❌ [备注]
- ...

发现问题：
1. [问题描述]
2. [问题描述]

建议改进：
1. [改进建议]
2. [改进建议]
```

## 注意事项

1. 测试时请确保网络连接稳定
2. 建议在不同设备和屏幕尺寸下测试
3. 注意观察控制台是否有错误信息
4. 测试过程中可以打开开发者工具监控网络请求
5. 如发现问题，请记录详细的复现步骤
