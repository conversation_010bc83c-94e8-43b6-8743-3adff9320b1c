generator client {
  provider        = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model GeneratedApp {
  id        String   @id @default(nanoid(5))
  model     String
  prompt    String
  code      String
  createdAt DateTime @default(now())

  @@index([id])
}

model Chat {
  id                String    @id @default(nanoid(16))
  model             String
  quality           String
  prompt            String
  title             String
  llamaCoderVersion String    @default("v2")
  shadcn            Boolean
  messages          Message[]
  createdAt         DateTime  @default(now())
}

model Message {
  id        String   @id @default(nanoid(16))
  role      String
  content   String
  chatId    String
  chat      Chat     @relation(fields: [chatId], references: [id], onDelete: Cascade)
  position  Int
  createdAt DateTime @default(now())

  @@index([chatId])
}
