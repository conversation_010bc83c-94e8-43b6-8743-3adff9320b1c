<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="800" viewBox="0 0 400 800" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect x="0" y="0" width="400" height="800" fill="#f5f7fa"/>
  
  <!-- 页面容器 -->
  <rect x="10" y="10" width="380" height="780" rx="8" fill="white" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 顶部导航栏 -->
  <rect x="10" y="10" width="380" height="50" rx="8 8 0 0" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  <text x="30" y="40" font-family="Arial" font-size="18" font-weight="bold" fill="#333">内容生成器</text>
  
  <!-- 标签切换 -->
  <rect x="10" y="60" width="380" height="40" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
  
  <rect x="20" y="65" width="180" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="110" y="85" font-family="Arial" font-size="14" fill="white" text-anchor="middle">输入</text>
  
  <rect x="200" y="65" width="180" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="290" y="85" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">预览</text>
  
  <!-- 输入面板 -->
  <rect x="10" y="100" width="380" height="690" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
  
  <!-- 输入面板内容 -->
  <!-- 标题 -->
  <text x="30" y="130" font-family="Arial" font-size="16" font-weight="bold" fill="#333">输入您的需求</text>
  
  <!-- 需求输入框 -->
  <rect x="30" y="150" width="340" height="150" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="40" y="175" font-family="Arial" font-size="14" fill="#666" opacity="0.7">请描述您想要生成的内容...</text>
  
  <!-- 内容类型选择 -->
  <text x="30" y="330" font-family="Arial" font-size="16" font-weight="bold" fill="#333">内容类型</text>
  
  <!-- HTML按钮 -->
  <rect x="30" y="345" width="160" height="40" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="110" y="370" font-family="Arial" font-size="14" fill="white" text-anchor="middle">HTML</text>
  
  <!-- Markdown按钮 -->
  <rect x="210" y="345" width="160" height="40" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="290" y="370" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">Markdown</text>
  
  <!-- 高级选项 -->
  <text x="30" y="415" font-family="Arial" font-size="16" font-weight="bold" fill="#333">高级选项</text>
  
  <!-- 风格选择 -->
  <text x="30" y="445" font-family="Arial" font-size="14" fill="#555">风格</text>
  <rect x="30" y="455" width="340" height="40" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="50" y="480" font-family="Arial" font-size="14" fill="#333">简约现代</text>
  <path d="M350 475 L360 465 L370 475" fill="none" stroke="#666" stroke-width="2"/>
  
  <!-- 复杂度选择 -->
  <text x="30" y="515" font-family="Arial" font-size="14" fill="#555">复杂度</text>
  <rect x="30" y="525" width="340" height="40" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="50" y="550" font-family="Arial" font-size="14" fill="#333">中等</text>
  <path d="M350 545 L360 535 L370 545" fill="none" stroke="#666" stroke-width="2"/>
  
  <!-- 主题选择 -->
  <text x="30" y="585" font-family="Arial" font-size="14" fill="#555">主题</text>
  <rect x="30" y="595" width="340" height="40" rx="4" fill="#f9f9f9" stroke="#d0d0d0" stroke-width="1"/>
  <text x="50" y="620" font-family="Arial" font-size="14" fill="#333">浅色</text>
  <path d="M350 615 L360 605 L370 615" fill="none" stroke="#666" stroke-width="2"/>
  
  <!-- 生成按钮 -->
  <rect x="30" y="665" width="340" height="50" rx="4" fill="#4a6cf7" stroke="none"/>
  <text x="200" y="695" font-family="Arial" font-size="16" font-weight="bold" fill="white" text-anchor="middle">生成内容</text>
  
  <!-- 历史记录按钮 -->
  <rect x="30" y="735" width="160" height="40" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="110" y="760" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">历史记录</text>
  
  <!-- 保存按钮 -->
  <rect x="210" y="735" width="160" height="40" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
  <text x="290" y="760" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">保存内容</text>
  
  <!-- 预览面板 (隐藏状态) -->
  <g opacity="0">
    <rect x="10" y="100" width="380" height="690" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
    
    <!-- 预览工具栏 -->
    <rect x="10" y="100" width="380" height="40" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    
    <rect x="20" y="105" width="110" height="30" rx="4" fill="#4a6cf7" stroke="none"/>
    <text x="75" y="125" font-family="Arial" font-size="14" fill="white" text-anchor="middle">代码</text>
    
    <rect x="135" y="105" width="110" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
    <text x="190" y="125" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">预览</text>
    
    <rect x="250" y="105" width="110" height="30" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
    <text x="305" y="125" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">分屏</text>
    
    <!-- 代码内容 -->
    <rect x="20" y="150" width="360" height="570" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    
    <!-- 代码行号 -->
    <rect x="20" y="150" width="30" height="570" fill="#f0f0f0" stroke="none"/>
    <text x="35" y="175" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">1</text>
    <text x="35" y="195" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">2</text>
    <text x="35" y="215" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">3</text>
    <text x="35" y="235" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">4</text>
    <text x="35" y="255" font-family="Consolas, monospace" font-size="12" fill="#999" text-anchor="middle">5</text>
    
    <!-- 代码内容 -->
    <text x="60" y="175" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;!DOCTYPE html&gt;</tspan></text>
    <text x="60" y="195" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;html&gt;</tspan></text>
    <text x="60" y="215" font-family="Consolas, monospace" font-size="12" fill="#333"><tspan fill="#0000ff">&lt;head&gt;</tspan></text>
    <text x="60" y="235" font-family="Consolas, monospace" font-size="12" fill="#333">  <tspan fill="#0000ff">&lt;title&gt;</tspan>我的产品展示页面<tspan fill="#0000ff">&lt;/title&gt;</tspan></text>
    <text x="60" y="255" font-family="Consolas, monospace" font-size="12" fill="#333">  <tspan fill="#0000ff">&lt;style&gt;</tspan></text>
    
    <!-- 底部操作栏 -->
    <rect x="10" y="730" width="380" height="60" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    
    <rect x="20" y="740" width="110" height="40" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
    <text x="75" y="765" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">复制</text>
    
    <rect x="140" y="740" width="110" height="40" rx="4" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
    <text x="195" y="765" font-family="Arial" font-size="14" fill="#333" text-anchor="middle">下载</text>
    
    <rect x="260" y="740" width="110" height="40" rx="4" fill="#4a6cf7" stroke="none"/>
    <text x="315" y="765" font-family="Arial" font-size="14" fill="white" text-anchor="middle">返回编辑</text>
  </g>
  
  <!-- 历史记录弹窗 (隐藏状态) -->
  <g opacity="0">
    <rect x="20" y="150" width="360" height="500" rx="8" fill="white" stroke="#e0e0e0" stroke-width="2" filter="drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1))"/>
    <text x="40" y="180" font-family="Arial" font-size="18" font-weight="bold" fill="#333">历史记录</text>
    <line x1="20" y1="200" x2="380" y2="200" stroke="#e0e0e0" stroke-width="1"/>
    
    <!-- 历史记录项目 -->
    <rect x="40" y="220" width="320" height="60" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    <text x="60" y="245" font-family="Arial" font-size="14" font-weight="bold" fill="#333">产品展示页面</text>
    <text x="60" y="265" font-family="Arial" font-size="12" fill="#666">HTML • 2023-06-15 14:30</text>
    
    <rect x="40" y="290" width="320" height="60" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    <text x="60" y="315" font-family="Arial" font-size="14" font-weight="bold" fill="#333">项目说明文档</text>
    <text x="60" y="335" font-family="Arial" font-size="12" fill="#666">Markdown • 2023-06-14 10:15</text>
    
    <rect x="40" y="360" width="320" height="60" rx="4" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="1"/>
    <text x="60" y="385" font-family="Arial" font-size="14" font-weight="bold" fill="#333">团队介绍页面</text>
    <text x="60" y="405" font-family="Arial" font-size="12" fill="#666">HTML • 2023-06-12 16:45</text>
    
    <!-- 关闭按钮 -->
    <circle cx="360" cy="180" r="15" fill="#f0f0f0" stroke="#d0d0d0" stroke-width="1"/>
    <text x="360" y="185" font-family="Arial" font-size="16" fill="#666" text-anchor="middle">×</text>
    
    <!-- 底部按钮 -->
    <rect x="40" y="580" width="320" height="50" rx="4" fill="#4a6cf7" stroke="none"/>
    <text x="200" y="610" font-family="Arial" font-size="16" fill="white" text-anchor="middle">关闭</text>
  </g>
  
  <!-- 加载状态 (隐藏状态) -->
  <g opacity="0">
    <rect x="0" y="0" width="400" height="800" fill="rgba(0,0,0,0.5)"/>
    <rect x="50" y="350" width="300" height="100" rx="8" fill="white" stroke="#e0e0e0" stroke-width="1"/>
    <text x="200" y="390" font-family="Arial" font-size="16" fill="#333" text-anchor="middle">正在生成内容，请稍候...</text>
    
    <!-- 加载动画 -->
    <circle cx="160" cy="420" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0s"/>
    </circle>
    <circle cx="180" cy="420" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0.2s"/>
    </circle>
    <circle cx="200" cy="420" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0.4s"/>
    </circle>
    <circle cx="220" cy="420" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0.6s"/>
    </circle>
    <circle cx="240" cy="420" r="5" fill="#4a6cf7">
      <animate attributeName="opacity" values="1;0.2;1" dur="1.5s" repeatCount="indefinite" begin="0.8s"/>
    </circle>
  </g>
</svg>
