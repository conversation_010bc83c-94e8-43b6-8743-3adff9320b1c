# 内容查看器组件技术文档

## 1. 组件概述

内容查看器（ContentViewer）是一个用于展示和预览代码的React组件，支持HTML和Markdown内容的源代码显示和渲染预览。该组件提供了多种视图模式和交互功能，适用于文档编辑、教程展示或代码示例等场景。

## 2. 功能特性

### 2.1 内容类型支持
- **HTML**：支持完整的HTML标签和CSS样式
- **Markdown**：支持标准Markdown语法和扩展功能

### 2.2 视图模式
- **代码模式**：只显示源代码，支持语法高亮
- **预览模式**：只显示渲染结果
- **分屏模式**：同时显示源代码和渲染结果，可调整分割比例

### 2.3 交互功能
- 内容类型切换（HTML/Markdown）
- 视图模式切换（代码/预览/分屏）
- 分屏比例调整（通过拖动分割线）
- 复制代码到剪贴板
- 刷新预览

### 2.4 技术特性
- 代码语法高亮（支持多种编程语言）
- 安全的HTML渲染（使用iframe沙箱）
- 丰富的Markdown渲染（支持扩展语法）
- 响应式设计
- 客户端渲染（避免SSR水合错误）

## 3. 组件结构

```
components/content-viewer/
├── content-viewer.tsx    # 主组件
├── code-display.tsx      # 代码显示组件
├── html-preview.tsx      # HTML预览组件
├── markdown-preview.tsx  # Markdown预览组件
├── toolbar.tsx           # 工具栏组件
├── status-bar.tsx        # 状态栏组件
├── index.ts              # 导出文件
└── types.ts              # 类型定义
```

## 4. 安装依赖

组件依赖以下npm包：

```bash
npm install react-syntax-highlighter react-markdown
```

## 5. 使用方法

### 5.1 基本用法

```tsx
import dynamic from 'next/dynamic';
import { ContentType, ViewMode } from '@/components/content-viewer/types';

// 使用动态导入加载ContentViewer组件，并禁用SSR
const ContentViewer = dynamic(
  () => import('@/components/content-viewer/content-viewer'),
  { ssr: false }
);

// 在组件中使用
function MyComponent() {
  return (
    <div className="h-[600px]">
      <ContentViewer 
        content="<h1>Hello World</h1>"
        contentType="html"
        initialViewMode="split"
      />
    </div>
  );
}
```

### 5.2 完整配置

```tsx
<ContentViewer 
  // 必需属性
  content={htmlOrMarkdownContent}
  contentType="html" // 或 "markdown"
  
  // 可选属性
  initialViewMode="split" // "code", "preview", 或 "split"
  editable={false} // 是否允许编辑内容
  theme="light" // "light" 或 "dark"
  splitRatio={50} // 分屏比例（0-100）
  
  // 事件回调
  onChange={(newContent) => console.log(newContent)} // 内容变化时触发
  onContentTypeChange={(type) => console.log(type)} // 内容类型变化时触发
  onViewModeChange={(mode) => console.log(mode)} // 视图模式变化时触发
/>
```

## 6. API参考

### 6.1 ContentViewer 组件

#### 属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| content | string | 必需 | 要显示的HTML或Markdown内容 |
| contentType | "html" \| "markdown" | 必需 | 内容类型 |
| initialViewMode | "code" \| "preview" \| "split" | "split" | 初始视图模式 |
| editable | boolean | false | 是否允许编辑内容 |
| theme | "light" \| "dark" | "light" | 主题 |
| splitRatio | number | 50 | 分屏比例（0-100） |
| onChange | (content: string) => void | undefined | 内容变化时的回调函数 |
| onContentTypeChange | (type: ContentType) => void | undefined | 内容类型变化时的回调函数 |
| onViewModeChange | (mode: ViewMode) => void | undefined | 视图模式变化时的回调函数 |

### 6.2 子组件

#### CodeDisplay

显示代码并提供语法高亮功能。

```tsx
<CodeDisplay 
  content="const hello = 'world';"
  language="javascript"
  editable={false}
  onChange={(newCode) => console.log(newCode)}
/>
```

#### HtmlPreview

在安全的iframe环境中渲染HTML内容。

```tsx
<HtmlPreview content="<h1>Hello World</h1>" />
```

#### MarkdownPreview

渲染Markdown内容。

```tsx
<MarkdownPreview content="# Hello World" />
```

#### Toolbar

提供内容类型和视图模式切换功能。

```tsx
<Toolbar 
  contentType="html"
  viewMode="split"
  onContentTypeChange={(type) => setContentType(type)}
  onViewModeChange={(mode) => setViewMode(mode)}
/>
```

#### StatusBar

显示状态信息和提供操作按钮。

```tsx
<StatusBar 
  contentType="html"
  lineCount={10}
  encoding="UTF-8"
  onCopyCode={() => navigator.clipboard.writeText(content)}
  onRefresh={() => {/* 刷新逻辑 */}}
/>
```

## 7. 类型定义

```typescript
// 内容类型
export type ContentType = 'html' | 'markdown';

// 视图模式
export type ViewMode = 'code' | 'preview' | 'split';

// ContentViewer组件属性
export interface ContentViewerProps {
  content: string;
  contentType: ContentType;
  initialViewMode?: ViewMode;
  editable?: boolean;
  theme?: 'light' | 'dark';
  splitRatio?: number;
  onChange?: (content: string) => void;
  onContentTypeChange?: (type: ContentType) => void;
  onViewModeChange?: (mode: ViewMode) => void;
}
```

## 8. 注意事项

### 8.1 服务器端渲染

为避免水合错误，建议使用Next.js的`dynamic`导入并禁用SSR：

```tsx
import dynamic from 'next/dynamic';

const ContentViewer = dynamic(
  () => import('@/components/content-viewer/content-viewer'),
  { ssr: false }
);
```

### 8.2 容器高度

组件需要一个具有固定高度或百分比高度的容器：

```tsx
<div className="h-[600px]">
  <ContentViewer ... />
</div>
```

### 8.3 Markdown支持

默认支持基本的Markdown语法。如需支持更多扩展语法（如表格、任务列表等），可能需要添加额外的插件。

### 8.4 安全性

HTML预览使用iframe沙箱进行隔离，但仍建议不要渲染不受信任的HTML内容。

## 9. 性能优化

- 使用`React.memo`减少不必要的重渲染
- 代码高亮和Markdown渲染组件使用动态导入
- 大型文档可能会影响性能，考虑实现虚拟滚动

## 10. 浏览器兼容性

- 支持所有现代浏览器（Chrome、Firefox、Safari、Edge）
- 不支持IE11及以下版本

## 11. 示例

### 11.1 HTML示例

```tsx
const htmlExample = `<!DOCTYPE html>
<html>
<head>
  <title>示例页面</title>
  <style>
    body { font-family: Arial; }
    h1 { color: blue; }
  </style>
</head>
<body>
  <h1>Hello World</h1>
  <p>这是一个HTML示例</p>
</body>
</html>`;

<ContentViewer 
  content={htmlExample}
  contentType="html"
  initialViewMode="split"
/>
```

### 11.2 Markdown示例

```tsx
const markdownExample = `# Markdown示例

这是一个**Markdown**示例文档。

## 列表

- 项目一
- 项目二
- 项目三

## 代码

\`\`\`javascript
console.log('Hello World');
\`\`\`
`;

<ContentViewer 
  content={markdownExample}
  contentType="markdown"
  initialViewMode="split"
/>
```

## 12. 故障排除

### 问题：组件不显示或显示空白

**可能原因**：
- 容器高度未设置
- 内容为空

**解决方案**：
- 确保容器有明确的高度
- 检查传入的内容是否为空

### 问题：HTML预览不正确

**可能原因**：
- HTML语法错误
- iframe加载问题

**解决方案**：
- 检查HTML语法
- 确保没有阻止iframe加载的内容安全策略

### 问题：Markdown渲染不完整

**可能原因**：
- 使用了不支持的Markdown扩展语法
- 语法错误

**解决方案**：
- 检查Markdown语法
- 考虑添加额外的Markdown插件支持特定语法

## 13. 未来改进方向

- 添加更多Markdown插件支持
- 实现代码编辑器的行号和语法检查
- 添加导出功能（PDF、图片等）
- 添加主题切换功能
- 支持更多文件格式（如CSV、JSON等）
- 添加协作编辑功能

---

本文档提供了内容查看器组件的全面技术参考。如有任何问题或建议，请随时联系开发团队。
