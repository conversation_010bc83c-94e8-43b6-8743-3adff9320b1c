# 自定义模型保存问题修复测试

## 问题描述
新增的模型，下次进来看不到了

## 问题原因分析
1. **重复加载问题**: 每次打开AI设置对话框时，`useEffect`都会重新从Cookie加载配置
2. **状态覆盖**: `setLocalModels(config.customModels || [])`会覆盖用户新添加但未保存的模型
3. **时机问题**: 用户添加模型后如果没有立即保存就关闭对话框，再次打开时新模型会丢失

## 修复方案

### 1. 添加初始化状态跟踪
```typescript
const [isInitialized, setIsInitialized] = useState(false);
```

### 2. 修改加载逻辑
```typescript
// 只在首次初始化时从Cookie加载自定义模型
if (!isInitialized) {
  setLocalModels(config.customModels || []);
  setIsInitialized(true);
}
```

### 3. 保存后重置初始化状态
```typescript
// 重置初始化状态，以便下次打开时能正确加载已保存的配置
setIsInitialized(false);
```

### 4. 提供商变更时重新加载
```typescript
// 重新加载自定义模型（提供商变更时需要重新加载）
setLocalModels(config.customModels || []);
```

## 修复后的行为

### 场景1: 添加新模型但未保存
1. 用户打开AI设置
2. 添加新模型（如：gemini-2.0-flash）
3. 关闭对话框（未保存）
4. 再次打开AI设置
5. **预期结果**: 新模型仍然存在（不会被覆盖）

### 场景2: 添加新模型并保存
1. 用户打开AI设置
2. 添加新模型
3. 点击保存
4. 关闭对话框
5. 再次打开AI设置
6. **预期结果**: 新模型正确显示（从Cookie加载）

### 场景3: 切换提供商
1. 用户在OpenAI提供商下添加模型
2. 切换到其他提供商
3. 再切换回OpenAI
4. **预期结果**: 之前添加的模型正确显示

### 场景4: 页面刷新后
1. 用户添加并保存新模型
2. 刷新页面
3. 打开AI设置
4. **预期结果**: 保存的模型正确显示

## 测试步骤

### 测试1: 未保存模型的持久性
1. 打开AI设置
2. 添加自定义模型：
   - 名称: "Gemini 2.0 Flash"
   - ID: "gemini-2.0-flash"
   - 提供商: OpenAI
   - maxTokens: 8192
   - temperature: 0.7
3. **不要点击保存**，直接关闭对话框
4. 再次打开AI设置
5. 检查新模型是否还在列表中

### 测试2: 保存模型的持久性
1. 在测试1的基础上，点击保存
2. 关闭对话框
3. 刷新页面
4. 打开AI设置
5. 检查模型是否正确加载

### 测试3: 提供商切换
1. 在OpenAI提供商下添加模型
2. 切换到DeepSeek提供商
3. 再切换回OpenAI提供商
4. 检查模型是否还在

### 测试4: 模型选择和使用
1. 添加并保存自定义模型
2. 在模型选择器中选择该模型
3. 进行对话测试
4. 检查是否能正常工作

## 技术细节

### Cookie存储格式
```json
{
  "ai_custom_models": "[{\"id\":\"gemini-2.0-flash\",\"name\":\"Gemini 2.0 Flash\",\"provider\":\"openai\",\"maxTokens\":8192,\"temperature\":0.7}]"
}
```

### 状态管理流程
1. **初始化**: `isInitialized = false`
2. **首次打开**: 从Cookie加载 → `isInitialized = true`
3. **后续打开**: 跳过Cookie加载，保持本地状态
4. **保存成功**: `isInitialized = false`，下次打开重新加载
5. **提供商变更**: 强制重新加载

### 错误处理
- JSON解析失败时使用空数组
- Cookie不存在时使用空数组
- 网络错误时保持当前状态

## 预期修复效果

修复后，用户应该能够：
1. ✅ 添加自定义模型并看到它们持久保存
2. ✅ 在未保存时不会丢失新添加的模型
3. ✅ 在保存后能正确加载已保存的模型
4. ✅ 在切换提供商时正确显示相应的模型
5. ✅ 在页面刷新后正确恢复所有配置

## 验证清单

- [ ] 添加模型后未保存，重新打开对话框模型仍存在
- [ ] 添加模型后保存，刷新页面后模型仍存在
- [ ] 切换提供商后模型正确显示
- [ ] 删除模型功能正常工作
- [ ] 模型选择和对话功能正常
- [ ] 配置导入/导出功能正常
- [ ] 错误情况下不会丢失数据

修复完成后，自定义模型的保存和加载应该完全可靠，用户不会再遇到"新增的模型，下次进来看不到了"的问题。
