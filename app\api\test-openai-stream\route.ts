import { cookies } from "next/headers";
import { AIConfigManager, COOKIE_KEYS } from "@/lib/ai-config";
import { ProviderFactory, ProviderType } from "@/lib/provider-factory";

export async function POST(req: Request) {
  try {
    // 解析请求体
    const { prompt, model, provider } = await req.json();
    
    console.log("收到流式API请求", { prompt: prompt.substring(0, 50) + "...", model, provider });
    
    // 验证必要参数
    if (!prompt) {
      console.error("缺少必要参数: prompt");
      return new Response(
        JSON.stringify({ error: "缺少必要参数: prompt" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
    
    // 获取配置
    // 直接从cookies中获取所需的值
    const cookieStore = await cookies();
    const apiKeyKey = `${COOKIE_KEYS.API_KEY_PREFIX}${provider}`;
    const baseUrlKey = `${COOKIE_KEYS.BASE_URL_PREFIX}${provider}`;
    
    const apiKey = cookieStore.get(apiKeyKey)?.value;
    const baseUrl = cookieStore.get(baseUrlKey)?.value;
    
    console.log("配置信息", { 
      provider, 
      hasApiKey: !!apiKey, 
      apiKeyType: typeof apiKey,
      hasBaseUrl: !!baseUrl 
    });
    
    // 验证API密钥
    if (provider === "openai" && !apiKey) {
      console.error("使用OpenAI API需要提供有效的API密钥");
      return new Response(
        JSON.stringify({ error: "使用OpenAI API需要提供有效的API密钥" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
    
    console.log("准备获取Provider实例", { provider });
    
    // 获取Provider实例
    const providerInstance = await ProviderFactory.getProvider({
      type: provider as ProviderType,
      apiKey,
      baseUrl
    });
    
    console.log("Provider实例创建成功", { provider });
    
    // 构建消息
    const messages = [
      {
        role: "system",
        content: "You are a helpful assistant.",
      },
      {
        role: "user",
        content: prompt,
      },
    ];
    
    console.log("准备创建流式响应", { provider, model });
    
    // 创建流式响应
    let stream: any;
    
    if (provider === "openai") {
      const openaiInstance = providerInstance as any;
      try {
        stream = await openaiInstance.chat.completions.create({
          model,
          messages,
          temperature: 0.7,
          max_tokens: 1000,
          stream: true,
        });
        console.log("OpenAI流式响应创建成功");
      } catch (error) {
        console.error("OpenAI流式响应创建失败", error);
        throw error;
      }
    } else if (provider === "together") {
      const togetherInstance = providerInstance as any;
      try {
        stream = await togetherInstance.chat.completions.create({
          model,
          messages,
          temperature: 0.7,
          max_tokens: 1000,
          stream: true,
        });
        console.log("Together流式响应创建成功");
      } catch (error) {
        console.error("Together流式响应创建失败", error);
        throw error;
      }
    } else {
      console.error(`不支持的提供商: ${provider}`);
      return new Response(
        JSON.stringify({ error: `不支持的提供商: ${provider}` }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }
    
    console.log("开始处理流式响应");
    
    // 创建响应流
    const encoder = new TextEncoder();
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          // 处理流式响应
          for await (const chunk of stream) {
            // 提取内容
            let content = "";
            
            if (provider === "openai") {
              content = chunk.choices[0]?.delta?.content || "";
            } else if (provider === "together") {
              content = chunk.choices[0]?.delta?.content || "";
            }
            
            // 发送内容
            if (content) {
              controller.enqueue(encoder.encode(content));
            }
          }
          
          console.log("流式响应处理完成");
          controller.close();
        } catch (error) {
          console.error("流式响应错误:", error);
          controller.error(error);
        }
      },
    });
    
    console.log("返回流式响应");
    
    // 返回流式响应
    return new Response(readableStream, {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
        "Cache-Control": "no-cache",
      },
    });
  } catch (error) {
    console.error("API调用错误:", error);
    return new Response(
      JSON.stringify({ error: error instanceof Error ? error.message : "未知错误" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}

export const maxDuration = 45;
